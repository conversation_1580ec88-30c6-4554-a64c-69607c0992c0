-- Useful views for the application

-- Appointment calendar view
CREATE OR REPLACE VIEW appointment_calendar AS
SELECT 
    a.id as appointment_id,
    a.start_time,
    a.end_time,
    a.status,
    a.price,
    a.notes,
    c.id as client_id,
    c.full_name as client_name,
    c.phone as client_phone,
    u.id as stylist_id,
    u.full_name as stylist_name,
    s.id as service_id,
    s.name as service_name,
    s.category as service_category,
    s.duration_minutes
FROM appointments a
JOIN clients c ON a.client_id = c.id
JOIN users u ON a.stylist_id = u.id
JOIN services s ON a.service_id = s.id;

-- Client history view
CREATE OR REPLACE VIEW client_history AS
SELECT 
    c.id as client_id,
    c.full_name as client_name,
    a.id as appointment_id,
    a.start_time as appointment_date,
    a.status as appointment_status,
    s.name as service_name,
    u.full_name as stylist_name,
    con.id as consultation_id,
    sr.after_images,
    sr.client_satisfaction,
    sr.client_feedback,
    cf.formula
FROM clients c
LEFT JOIN appointments a ON c.id = a.client_id
LEFT JOIN services s ON a.service_id = s.id
LEFT JOIN users u ON a.stylist_id = u.id
LEFT JOIN consultations con ON con.appointment_id = a.id
LEFT JOIN service_results sr ON sr.consultation_id = con.id
LEFT JOIN color_formulations cf ON cf.consultation_id = con.id AND cf.is_final = true
ORDER BY a.start_time DESC;

-- Inventory status view
CREATE OR REPLACE VIEW inventory_status AS
SELECT 
    si.id,
    si.salon_id,
    p.id as product_id,
    p.name as product_name,
    p.sku,
    b.name as brand_name,
    pl.name as line_name,
    si.current_stock,
    si.min_stock_level,
    si.max_stock_level,
    CASE 
        WHEN si.current_stock = 0 THEN 'out_of_stock'
        WHEN si.current_stock <= si.min_stock_level THEN 'low_stock'
        ELSE 'in_stock'
    END as stock_status,
    si.last_restocked,
    si.location
FROM salon_inventory si
JOIN products p ON si.product_id = p.id
JOIN product_lines pl ON p.line_id = pl.id
JOIN brands b ON pl.brand_id = b.id;

-- Analytics helper functions

-- Calculate service price with dynamic factors
CREATE OR REPLACE FUNCTION calculate_service_price(
    p_service_id UUID,
    p_factors JSONB DEFAULT '{}'
) RETURNS DECIMAL AS $$
DECLARE
    v_base_price DECIMAL;
    v_final_price DECIMAL;
    v_adjustment RECORD;
BEGIN
    -- Get base price
    SELECT base_price INTO v_base_price
    FROM services
    WHERE id = p_service_id;
    
    v_final_price := v_base_price;
    
    -- Apply pricing adjustments based on factors
    -- This is a simplified version - expand as needed
    IF p_factors->>'hair_length' IS NOT NULL THEN
        CASE p_factors->>'hair_length'
            WHEN 'short' THEN v_final_price := v_final_price * 1.0;
            WHEN 'medium' THEN v_final_price := v_final_price * 1.2;
            WHEN 'long' THEN v_final_price := v_final_price * 1.5;
            WHEN 'extra_long' THEN v_final_price := v_final_price * 1.8;
        END CASE;
    END IF;
    
    IF p_factors->>'hair_density' IS NOT NULL THEN
        CASE p_factors->>'hair_density'
            WHEN 'thin' THEN v_final_price := v_final_price * 0.9;
            WHEN 'medium' THEN v_final_price := v_final_price * 1.0;
            WHEN 'thick' THEN v_final_price := v_final_price * 1.2;
        END CASE;
    END IF;
    
    RETURN v_final_price;
END;
$$ LANGUAGE plpgsql;

-- Get available time slots for a stylist
CREATE OR REPLACE FUNCTION get_available_slots(
    p_stylist_id UUID,
    p_date DATE,
    p_duration INTEGER -- minutes
) RETURNS TABLE(start_time TIMESTAMPTZ, end_time TIMESTAMPTZ) AS $$
DECLARE
    v_working_start TIME := '09:00'::TIME;
    v_working_end TIME := '18:00'::TIME;
    v_slot_start TIMESTAMPTZ;
    v_slot_end TIMESTAMPTZ;
BEGIN
    -- This is a simplified version
    -- In production, consider stylist working hours, breaks, etc.
    
    v_slot_start := p_date + v_working_start;
    
    WHILE v_slot_start + (p_duration || ' minutes')::INTERVAL <= p_date + v_working_end LOOP
        v_slot_end := v_slot_start + (p_duration || ' minutes')::INTERVAL;
        
        -- Check if slot is available
        IF NOT EXISTS (
            SELECT 1 FROM appointments
            WHERE stylist_id = p_stylist_id
            AND status NOT IN ('cancelled', 'no_show')
            AND (
                (start_time <= v_slot_start AND end_time > v_slot_start)
                OR (start_time < v_slot_end AND end_time >= v_slot_end)
                OR (start_time >= v_slot_start AND end_time <= v_slot_end)
            )
        ) THEN
            RETURN QUERY SELECT v_slot_start, v_slot_end;
        END IF;
        
        v_slot_start := v_slot_start + INTERVAL '15 minutes';
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Generate analytics dashboard data
CREATE OR REPLACE FUNCTION generate_analytics_report(
    p_salon_id UUID,
    p_period VARCHAR DEFAULT 'month',
    p_start_date DATE DEFAULT NULL,
    p_end_date DATE DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
    v_start_date DATE;
    v_end_date DATE;
    v_result JSONB;
BEGIN
    -- Set date range based on period
    IF p_start_date IS NULL OR p_end_date IS NULL THEN
        v_end_date := CURRENT_DATE;
        CASE p_period
            WHEN 'day' THEN v_start_date := v_end_date;
            WHEN 'week' THEN v_start_date := v_end_date - INTERVAL '7 days';
            WHEN 'month' THEN v_start_date := v_end_date - INTERVAL '1 month';
            WHEN 'year' THEN v_start_date := v_end_date - INTERVAL '1 year';
            ELSE v_start_date := v_end_date - INTERVAL '1 month';
        END CASE;
    ELSE
        v_start_date := p_start_date;
        v_end_date := p_end_date;
    END IF;
    
    -- Build analytics report
    v_result := jsonb_build_object(
        'period', p_period,
        'start_date', v_start_date,
        'end_date', v_end_date,
        'revenue', (
            SELECT jsonb_build_object(
                'total', COALESCE(SUM(price), 0),
                'average_ticket', COALESCE(AVG(price), 0),
                'by_service_category', (
                    SELECT jsonb_object_agg(category, total_revenue)
                    FROM (
                        SELECT s.category, COALESCE(SUM(a.price), 0) as total_revenue
                        FROM appointments a
                        JOIN services s ON a.service_id = s.id
                        WHERE a.salon_id = p_salon_id
                        AND a.status = 'completed'
                        AND a.start_time BETWEEN v_start_date AND v_end_date + INTERVAL '1 day'
                        GROUP BY s.category
                    ) t
                )
            )
            FROM appointments
            WHERE salon_id = p_salon_id
            AND status = 'completed'
            AND start_time BETWEEN v_start_date AND v_end_date + INTERVAL '1 day'
        ),
        'appointments', (
            SELECT jsonb_build_object(
                'total_count', COUNT(*),
                'completed_count', COUNT(*) FILTER (WHERE status = 'completed'),
                'cancelled_count', COUNT(*) FILTER (WHERE status = 'cancelled'),
                'no_show_count', COUNT(*) FILTER (WHERE status = 'no_show')
            )
            FROM appointments
            WHERE salon_id = p_salon_id
            AND start_time BETWEEN v_start_date AND v_end_date + INTERVAL '1 day'
        ),
        'clients', (
            SELECT jsonb_build_object(
                'total_clients', COUNT(DISTINCT c.id),
                'new_clients', COUNT(DISTINCT c.id) FILTER (
                    WHERE c.created_at BETWEEN v_start_date AND v_end_date + INTERVAL '1 day'
                )
            )
            FROM clients c
            WHERE c.salon_id = p_salon_id
        )
    );
    
    RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- Function to track inventory usage from consultation
CREATE OR REPLACE FUNCTION track_inventory_usage(
    p_consultation_id UUID
) RETURNS VOID AS $$
DECLARE
    v_formula JSONB;
    v_product JSONB;
    v_salon_id UUID;
BEGIN
    -- Get the final formula and salon_id
    SELECT cf.formula, c.salon_id 
    INTO v_formula, v_salon_id
    FROM color_formulations cf
    JOIN consultations c ON cf.consultation_id = c.id
    WHERE cf.consultation_id = p_consultation_id
    AND cf.is_final = true
    ORDER BY cf.created_at DESC
    LIMIT 1;
    
    IF v_formula IS NOT NULL THEN
        -- Process each product in the formula
        FOR v_product IN SELECT * FROM jsonb_array_elements(v_formula->'products')
        LOOP
            -- Create inventory movement
            INSERT INTO inventory_movements (
                salon_id,
                product_id,
                type,
                quantity,
                reference_type,
                reference_id,
                created_by
            )
            SELECT
                v_salon_id,
                (v_product->>'product_id')::UUID,
                'out'::movement_type,
                (v_product->>'amount_grams')::DECIMAL,
                'consultation',
                p_consultation_id,
                c.stylist_id
            FROM consultations c
            WHERE c.id = p_consultation_id;
            
            -- Update inventory stock
            UPDATE salon_inventory
            SET current_stock = current_stock - (v_product->>'amount_grams')::DECIMAL,
                updated_at = NOW()
            WHERE salon_id = v_salon_id
            AND product_id = (v_product->>'product_id')::UUID;
        END LOOP;
    END IF;
END;
$$ LANGUAGE plpgsql;