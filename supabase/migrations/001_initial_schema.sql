-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- <PERSON>reate custom types
CREATE TYPE user_role AS ENUM ('owner', 'stylist', 'receptionist');
CREATE TYPE appointment_status AS ENUM ('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show');
CREATE TYPE consultation_status AS ENUM ('draft', 'in_progress', 'completed', 'cancelled');
CREATE TYPE service_category AS ENUM ('color', 'cut', 'treatment', 'styling', 'consultation', 'package');
CREATE TYPE movement_type AS ENUM ('in', 'out', 'adjustment');
CREATE TYPE subscription_plan AS ENUM ('free', 'basic', 'premium', 'enterprise');
CREATE TYPE subscription_status AS ENUM ('active', 'cancelled', 'past_due');

-- Salons table
CREATE TABLE salons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    owner_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    address TEXT,
    phone VARCHAR(50),
    email VARCHAR(255),
    website VARCHAR(255),
    timezone VARCHAR(50) DEFAULT 'UTC',
    currency VARCHAR(3) DEFAULT 'USD',
    subscription_plan subscription_plan DEFAULT 'free',
    subscription_status subscription_status DEFAULT 'active',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Users table (extends auth.users)
CREATE TABLE users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    avatar_url TEXT,
    role user_role NOT NULL,
    salon_id UUID REFERENCES salons(id) ON DELETE SET NULL,
    bio TEXT,
    specialties TEXT[],
    certifications TEXT[],
    preferred_brands TEXT[],
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Clients table
CREATE TABLE clients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
    full_name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50) NOT NULL,
    date_of_birth DATE,
    notes TEXT,
    tags TEXT[],
    preferred_stylist_id UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES users(id),
    UNIQUE(salon_id, phone)
);

-- Client hair profiles
CREATE TABLE client_hair_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    natural_level INTEGER CHECK (natural_level >= 1 AND natural_level <= 10),
    undertone VARCHAR(20) CHECK (undertone IN ('warm', 'neutral', 'cool', 'ash')),
    gray_percentage INTEGER CHECK (gray_percentage >= 0 AND gray_percentage <= 100),
    hair_diameter VARCHAR(20) CHECK (hair_diameter IN ('fine', 'medium', 'coarse')),
    hair_density VARCHAR(20) CHECK (hair_density IN ('thin', 'medium', 'thick')),
    porosity VARCHAR(20) CHECK (porosity IN ('low', 'medium', 'high')),
    elasticity VARCHAR(20) CHECK (elasticity IN ('low', 'medium', 'high')),
    condition VARCHAR(20) CHECK (condition IN ('excellent', 'good', 'fair', 'poor')),
    scalp_condition TEXT,
    chemical_history JSONB DEFAULT '[]',
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(client_id)
);

-- Client allergies
CREATE TABLE client_allergies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    allergen VARCHAR(255) NOT NULL,
    severity VARCHAR(20) CHECK (severity IN ('mild', 'moderate', 'severe')),
    reaction_type VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Patch tests
CREATE TABLE patch_tests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    test_date TIMESTAMPTZ NOT NULL,
    products_tested TEXT[],
    result VARCHAR(20) CHECK (result IN ('negative', 'positive', 'pending')),
    reaction_details TEXT,
    performed_by UUID NOT NULL REFERENCES users(id),
    next_test_due DATE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Services
CREATE TABLE services (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category service_category NOT NULL,
    base_price DECIMAL(10, 2) NOT NULL,
    duration_minutes INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    requires_consultation BOOLEAN DEFAULT FALSE,
    deposit_percentage INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Appointments
CREATE TABLE appointments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    stylist_id UUID NOT NULL REFERENCES users(id),
    service_id UUID NOT NULL REFERENCES services(id),
    start_time TIMESTAMPTZ NOT NULL,
    end_time TIMESTAMPTZ NOT NULL,
    status appointment_status DEFAULT 'scheduled',
    notes TEXT,
    price DECIMAL(10, 2),
    deposit_amount DECIMAL(10, 2),
    deposit_paid BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES users(id)
);

-- Consultations
CREATE TABLE consultations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    stylist_id UUID NOT NULL REFERENCES users(id),
    appointment_id UUID REFERENCES appointments(id) ON DELETE SET NULL,
    status consultation_status DEFAULT 'draft',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

-- Hair diagnoses
CREATE TABLE hair_diagnoses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    consultation_id UUID NOT NULL REFERENCES consultations(id) ON DELETE CASCADE,
    images JSONB NOT NULL DEFAULT '[]',
    ai_analysis JSONB,
    stylist_assessment JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Color formulations
CREATE TABLE color_formulations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    consultation_id UUID NOT NULL REFERENCES consultations(id) ON DELETE CASCADE,
    target_result JSONB NOT NULL,
    formula JSONB NOT NULL,
    technique JSONB NOT NULL,
    estimated_cost DECIMAL(10, 2),
    estimated_time INTEGER, -- minutes
    created_at TIMESTAMPTZ DEFAULT NOW(),
    is_final BOOLEAN DEFAULT FALSE
);

-- Service results
CREATE TABLE service_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    consultation_id UUID NOT NULL REFERENCES consultations(id) ON DELETE CASCADE,
    before_images TEXT[],
    after_images TEXT[],
    actual_formula_used JSONB,
    actual_processing_time INTEGER, -- minutes
    client_satisfaction INTEGER CHECK (client_satisfaction >= 1 AND client_satisfaction <= 5),
    stylist_notes TEXT,
    client_feedback TEXT,
    follow_up_required BOOLEAN DEFAULT FALSE,
    next_service_recommendation TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Brands
CREATE TABLE brands (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    logo_url TEXT,
    website TEXT,
    is_professional BOOLEAN DEFAULT TRUE
);

-- Product lines
CREATE TABLE product_lines (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    brand_id UUID NOT NULL REFERENCES brands(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(50) CHECK (category IN ('color', 'developer', 'treatment', 'styling')),
    description TEXT,
    UNIQUE(brand_id, name)
);

-- Products
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    line_id UUID NOT NULL REFERENCES product_lines(id) ON DELETE CASCADE,
    sku VARCHAR(100),
    name VARCHAR(255) NOT NULL,
    shade_code VARCHAR(50),
    shade_name VARCHAR(255),
    size DECIMAL(10, 2) NOT NULL,
    unit VARCHAR(20) CHECK (unit IN ('grams', 'ml', 'oz')),
    cost_price DECIMAL(10, 2),
    retail_price DECIMAL(10, 2),
    is_active BOOLEAN DEFAULT TRUE,
    allergens TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Salon inventory
CREATE TABLE salon_inventory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id),
    current_stock DECIMAL(10, 2) DEFAULT 0,
    min_stock_level DECIMAL(10, 2) DEFAULT 0,
    max_stock_level DECIMAL(10, 2),
    location VARCHAR(255),
    last_restocked TIMESTAMPTZ,
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(salon_id, product_id)
);

-- Inventory movements
CREATE TABLE inventory_movements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id),
    type movement_type NOT NULL,
    quantity DECIMAL(10, 2) NOT NULL,
    reference_type VARCHAR(50),
    reference_id UUID,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES users(id)
);

-- Brand conversions
CREATE TABLE brand_conversions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_product_id UUID NOT NULL REFERENCES products(id),
    to_product_id UUID NOT NULL REFERENCES products(id),
    conversion_notes TEXT,
    confidence_level VARCHAR(20) CHECK (confidence_level IN ('high', 'medium', 'low')),
    verified_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(from_product_id, to_product_id)
);

-- Create indexes for better performance
CREATE INDEX idx_users_salon_id ON users(salon_id);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_clients_salon_id ON clients(salon_id);
CREATE INDEX idx_clients_phone ON clients(phone);
CREATE INDEX idx_appointments_salon_id ON appointments(salon_id);
CREATE INDEX idx_appointments_client_id ON appointments(client_id);
CREATE INDEX idx_appointments_stylist_id ON appointments(stylist_id);
CREATE INDEX idx_appointments_start_time ON appointments(start_time);
CREATE INDEX idx_consultations_salon_id ON consultations(salon_id);
CREATE INDEX idx_consultations_client_id ON consultations(client_id);
CREATE INDEX idx_inventory_movements_salon_id ON inventory_movements(salon_id);
CREATE INDEX idx_inventory_movements_product_id ON inventory_movements(product_id);

-- Enable Row Level Security on all tables
ALTER TABLE salons ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_hair_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_allergies ENABLE ROW LEVEL SECURITY;
ALTER TABLE patch_tests ENABLE ROW LEVEL SECURITY;
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE consultations ENABLE ROW LEVEL SECURITY;
ALTER TABLE hair_diagnoses ENABLE ROW LEVEL SECURITY;
ALTER TABLE color_formulations ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE salon_inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_movements ENABLE ROW LEVEL SECURITY;

-- Update triggers
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_salons_updated_at BEFORE UPDATE ON salons
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON clients
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_services_updated_at BEFORE UPDATE ON services
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_appointments_updated_at BEFORE UPDATE ON appointments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();