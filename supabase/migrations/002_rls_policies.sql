-- R<PERSON> Policies for multi-tenant security

-- Helper function to get user's salon_id
CREATE OR REPLACE FUNCTION get_user_salon_id(user_id UUID)
RETURNS UUID AS $$
BEGIN
    RETURN (SELECT salon_id FROM users WHERE id = user_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user belongs to salon
CREATE OR REPLACE FUNCTION user_belongs_to_salon(user_id UUID, salon_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users 
        WHERE id = user_id AND users.salon_id = salon_id
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Salons policies
CREATE POLICY "Users can view their own salon" ON salons
    FOR SELECT USING (
        id = get_user_salon_id(auth.uid())
    );

CREATE POLICY "Owners can update their salon" ON salons
    FOR UPDATE USING (
        owner_id = auth.uid()
    );

CREATE POLICY "New owners can insert salons" ON salons
    FOR INSERT WITH CHECK (
        owner_id = auth.uid()
    );

-- Users policies
CREATE POLICY "Users can view colleagues in same salon" ON users
    FOR SELECT USING (
        salon_id = get_user_salon_id(auth.uid())
        OR id = auth.uid()
    );

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (
        id = auth.uid()
    );

CREATE POLICY "New users can insert their profile" ON users
    FOR INSERT WITH CHECK (
        id = auth.uid()
    );

-- Clients policies
CREATE POLICY "Salon staff can view their clients" ON clients
    FOR SELECT USING (
        salon_id = get_user_salon_id(auth.uid())
    );

CREATE POLICY "Salon staff can insert clients" ON clients
    FOR INSERT WITH CHECK (
        salon_id = get_user_salon_id(auth.uid())
        AND created_by = auth.uid()
    );

CREATE POLICY "Salon staff can update their clients" ON clients
    FOR UPDATE USING (
        salon_id = get_user_salon_id(auth.uid())
    );

CREATE POLICY "Salon staff can delete their clients" ON clients
    FOR DELETE USING (
        salon_id = get_user_salon_id(auth.uid())
        AND (
            created_by = auth.uid()
            OR EXISTS (
                SELECT 1 FROM users 
                WHERE id = auth.uid() 
                AND role = 'owner'
            )
        )
    );

-- Client hair profiles policies
CREATE POLICY "Salon staff can manage client hair profiles" ON client_hair_profiles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM clients 
            WHERE clients.id = client_hair_profiles.client_id
            AND clients.salon_id = get_user_salon_id(auth.uid())
        )
    );

-- Client allergies policies
CREATE POLICY "Salon staff can manage client allergies" ON client_allergies
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM clients 
            WHERE clients.id = client_allergies.client_id
            AND clients.salon_id = get_user_salon_id(auth.uid())
        )
    );

-- Patch tests policies
CREATE POLICY "Salon staff can manage patch tests" ON patch_tests
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM clients 
            WHERE clients.id = patch_tests.client_id
            AND clients.salon_id = get_user_salon_id(auth.uid())
        )
    );

-- Services policies
CREATE POLICY "Anyone can view active services" ON services
    FOR SELECT USING (
        is_active = true
        AND salon_id = get_user_salon_id(auth.uid())
    );

CREATE POLICY "Owners and managers can manage services" ON services
    FOR ALL USING (
        salon_id = get_user_salon_id(auth.uid())
        AND EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('owner', 'stylist')
        )
    );

-- Appointments policies
CREATE POLICY "Salon staff can view appointments" ON appointments
    FOR SELECT USING (
        salon_id = get_user_salon_id(auth.uid())
    );

CREATE POLICY "Salon staff can create appointments" ON appointments
    FOR INSERT WITH CHECK (
        salon_id = get_user_salon_id(auth.uid())
        AND created_by = auth.uid()
    );

CREATE POLICY "Salon staff can update appointments" ON appointments
    FOR UPDATE USING (
        salon_id = get_user_salon_id(auth.uid())
        AND (
            stylist_id = auth.uid()
            OR created_by = auth.uid()
            OR EXISTS (
                SELECT 1 FROM users 
                WHERE id = auth.uid() 
                AND role IN ('owner', 'receptionist')
            )
        )
    );

-- Consultations policies
CREATE POLICY "Salon staff can view consultations" ON consultations
    FOR SELECT USING (
        salon_id = get_user_salon_id(auth.uid())
    );

CREATE POLICY "Stylists can manage their consultations" ON consultations
    FOR ALL USING (
        salon_id = get_user_salon_id(auth.uid())
        AND (
            stylist_id = auth.uid()
            OR EXISTS (
                SELECT 1 FROM users 
                WHERE id = auth.uid() 
                AND role = 'owner'
            )
        )
    );

-- Hair diagnoses policies
CREATE POLICY "Salon staff can manage diagnoses" ON hair_diagnoses
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM consultations 
            WHERE consultations.id = hair_diagnoses.consultation_id
            AND consultations.salon_id = get_user_salon_id(auth.uid())
        )
    );

-- Color formulations policies
CREATE POLICY "Salon staff can manage formulations" ON color_formulations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM consultations 
            WHERE consultations.id = color_formulations.consultation_id
            AND consultations.salon_id = get_user_salon_id(auth.uid())
        )
    );

-- Service results policies
CREATE POLICY "Salon staff can manage service results" ON service_results
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM consultations 
            WHERE consultations.id = service_results.consultation_id
            AND consultations.salon_id = get_user_salon_id(auth.uid())
        )
    );

-- Inventory policies
CREATE POLICY "Salon staff can view inventory" ON salon_inventory
    FOR SELECT USING (
        salon_id = get_user_salon_id(auth.uid())
    );

CREATE POLICY "Owners and stylists can manage inventory" ON salon_inventory
    FOR ALL USING (
        salon_id = get_user_salon_id(auth.uid())
        AND EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('owner', 'stylist')
        )
    );

-- Inventory movements policies
CREATE POLICY "Salon staff can view movements" ON inventory_movements
    FOR SELECT USING (
        salon_id = get_user_salon_id(auth.uid())
    );

CREATE POLICY "Salon staff can create movements" ON inventory_movements
    FOR INSERT WITH CHECK (
        salon_id = get_user_salon_id(auth.uid())
        AND created_by = auth.uid()
    );

-- Public tables (no RLS needed)
-- Brands, Product Lines, Products, and Brand Conversions are accessible to all authenticated users