{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "jsx": "react-native", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "noEmit": true, "paths": {"@salonier/ui": ["./packages/ui/src"], "@salonier/core": ["./packages/core/src"], "@salonier/types": ["./packages/types/src"]}}, "include": ["apps/**/*", "packages/**/*"], "exclude": ["node_modules", "**/node_modules"]}