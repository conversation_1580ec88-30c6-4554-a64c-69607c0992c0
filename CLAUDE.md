# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
```bash
# Start mobile development server
npm run dev:mobile

# Platform-specific development
npm run android  # Android development
npm run ios      # iOS development
npm run web      # Web browser development

# Clean and reinstall dependencies
npm run clean
npm run install:all
```

### Code Quality
```bash
# Run linting
npm run lint

# TypeScript type checking
npm run type-check
```

### Mobile App Commands (from apps/mobile/)
```bash
cd apps/mobile
npm start          # Start Expo
npm run lint       # Lint mobile app code
npm run type-check # Type check mobile app
```

## Architecture Overview

Salonier is an AI-powered hair color assistant and salon management SaaS platform built as a monorepo with the following structure:

### Tech Stack
- **Frontend**: React Native + Expo SDK 53 + Expo Router v4
- **Styling**: NativeWind v2 (Tailwind for React Native)
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Edge Functions)
- **AI**: OpenAI GPT-4o Vision & GPT-4 Turbo
- **State Management**: TanStack Query v5 + Zustand v4.4
- **Language**: TypeScript 5.8.3

### Package Structure
```
/
├── apps/mobile/          # Expo React Native app
│   ├── app/             # Expo Router file-based routing
│   │   ├── (auth)/      # Authentication screens
│   │   ├── (tabs)/      # Main tab navigation
│   │   ├── client/      # Client management
│   │   └── consultation/ # Hair consultation flow
│   ├── components/      # React Native components
│   ├── contexts/        # AuthContext, ThemeContext
│   └── utils/          # Utility functions
├── packages/           # Shared packages
│   ├── @salonier/core/ # Business logic, Supabase client, AI services
│   ├── @salonier/types/# TypeScript type definitions
│   └── @salonier/ui/   # Shared UI components and theme
└── supabase/          # Database migrations and functions
```

### Database Schema

Multi-tenant PostgreSQL database with Row Level Security (RLS):

**Core Tables**:
- `salons` - Salon accounts (multi-tenant root)
- `users` - Staff with roles (owner, stylist, receptionist)
- `clients` - Client records per salon
- `client_hair_profiles` - Detailed hair characteristics

**Service Management**:
- `appointments` - Scheduling system
- `consultations` - Hair analysis sessions
- `hair_diagnoses` - AI-powered and manual assessments
- `color_formulations` - Hair color recipes
- `service_results` - Before/after documentation

**Inventory**:
- `brands`, `product_lines`, `products` - Product catalog
- `salon_inventory` - Stock tracking per salon
- `brand_conversions` - Formula conversions between color brands

All tables enforce multi-tenant isolation through RLS policies based on salon_id.

### Key Development Patterns

1. **File-based Routing**: Uses Expo Router v4 - routes are defined by file structure in `apps/mobile/app/`
2. **Authentication**: Supabase Auth with secure token storage via Expo SecureStore
3. **API Calls**: Use TanStack Query for server state management with Supabase client
4. **Styling**: NativeWind (Tailwind) classes for consistent styling
5. **Type Safety**: All code should be fully typed with TypeScript
6. **AI Integration**: OpenAI calls are made through Supabase Edge Functions for security

### Environment Variables

Required in `.env` files:
- `EXPO_PUBLIC_SUPABASE_URL`
- `EXPO_PUBLIC_SUPABASE_ANON_KEY`
- `OPENAI_API_KEY` (server-side only)
- `STRIPE_PUBLISHABLE_KEY` (for payments)