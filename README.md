# Salonier - AI-Powered Hair Color Assistant & Salon Management Platform

Salonier is a comprehensive SaaS platform that revolutionizes hair coloring services and salon management through AI-powered technology. It combines precision color formulation, intelligent inventory management, and business analytics to empower beauty professionals.

## 🚀 Features

### Core Features (MVP)
- **AI Hair Analysis**: GPT-4 Vision powered hair diagnosis and color recommendations
- **Smart Formulation Engine**: Intelligent color formula generation with brand conversions
- **Client Management**: 360° client profiles with hair history and preferences
- **Appointment Scheduling**: Drag-and-drop calendar with multi-stylist support
- **Inventory Management**: Flexible tracking from simple brand selection to detailed stock control
- **Business Analytics**: Real-time dashboards with profitability insights

### Advanced Features
- **Safety Protocols**: Patch test workflows and allergy management
- **Brand Conversion**: Automatic formula conversion between different color lines
- **Cost Analysis**: Real-time profitability tracking per service
- **Multi-tenant Support**: Salon-wide management with role-based access
- **Offline Capabilities**: Essential features available without internet

## 🛠 Tech Stack

- **Frontend**: React Native with Expo SDK 53
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Edge Functions)
- **AI**: OpenAI GPT-4o Vision & GPT-4 Turbo
- **State Management**: TanStack Query + Zustand
- **UI Framework**: NativeWind + Custom component library
- **Architecture**: Monorepo with shared packages

## 📱 Platforms

- iOS (iPhone & iPad)
- Android (Phones & Tablets)  
- Web (Progressive Web App)

## 🏗 Project Structure

```
salonier/
├── apps/
│   ├── mobile/          # Expo React Native app
│   └── web/            # Next.js web app (future)
├── packages/
│   ├── ui/             # Shared UI components
│   ├── core/           # Business logic & services
│   └── types/          # TypeScript type definitions
└── supabase/
    ├── functions/      # Edge functions
    └── migrations/     # Database schema
```

## 🚦 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Expo CLI
- Supabase account
- OpenAI API key

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/salonier.git
cd salonier
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your API keys
```

4. Start the development server:
```bash
npm run dev:mobile
```

### Environment Variables

Create a `.env` file in the root with:

```env
# Supabase
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# OpenAI
EXPO_PUBLIC_OPENAI_API_KEY=your_openai_api_key

# Stripe (for payments)
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_key
```

## 📊 Database Schema

The application uses a comprehensive PostgreSQL schema with tables for:
- Users & Authentication
- Salons & Staff Management
- Clients & Hair Profiles
- Consultations & Formulations
- Appointments & Services
- Inventory & Products
- Analytics & Reporting

## 🔐 Security & Privacy

- Client-side face detection and blurring before AI processing
- Row Level Security (RLS) on all database operations
- JWT tokens with short expiry times
- GDPR/CCPA compliant data handling
- Encrypted storage for sensitive data

## 🧪 Testing

```bash
# Run unit tests
npm test

# Run E2E tests
npm run test:e2e

# Type checking
npm run type-check

# Linting
npm run lint
```

## 📦 Building for Production

```bash
# Build for iOS
npm run build:ios

# Build for Android
npm run build:android

# Build for Web
npm run build:web
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenAI for GPT-4 Vision API
- Supabase for the backend infrastructure
- Expo team for the amazing development experience
- All the hair color professionals who provided insights

## 📞 Support

- Documentation: [docs.salonier.com](https://docs.salonier.com)
- Email: <EMAIL>
- Discord: [Join our community](https://discord.gg/salonier)

---

Built with ❤️ for hair color professionals worldwide