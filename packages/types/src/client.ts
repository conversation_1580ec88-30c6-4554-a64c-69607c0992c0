export interface Client {
  id: string;
  salon_id: string;
  full_name: string;
  email?: string;
  phone: string;
  date_of_birth?: string;
  notes?: string;
  tags?: string[];
  preferred_stylist_id?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
}

export interface ClientHairProfile {
  id: string;
  client_id: string;
  natural_level: number; // 1-10
  undertone: 'warm' | 'neutral' | 'cool' | 'ash';
  gray_percentage: number; // 0-100
  hair_diameter: 'fine' | 'medium' | 'coarse';
  hair_density: 'thin' | 'medium' | 'thick';
  porosity: 'low' | 'medium' | 'high';
  elasticity: 'low' | 'medium' | 'high';
  condition: 'excellent' | 'good' | 'fair' | 'poor';
  scalp_condition?: string;
  chemical_history?: ChemicalHistory[];
  updated_at: string;
}

export interface ChemicalHistory {
  date: string;
  service: string;
  products_used: string[];
  notes?: string;
}

export interface ClientAllergy {
  id: string;
  client_id: string;
  allergen: string;
  severity: 'mild' | 'moderate' | 'severe';
  reaction_type: string;
  notes?: string;
  created_at: string;
}

export interface PatchTest {
  id: string;
  client_id: string;
  test_date: string;
  products_tested: string[];
  result: 'negative' | 'positive' | 'pending';
  reaction_details?: string;
  performed_by: string;
  next_test_due?: string;
}