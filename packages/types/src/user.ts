export type UserRole = 'owner' | 'stylist' | 'receptionist';

export interface User {
  id: string;
  email: string;
  full_name: string;
  phone?: string;
  avatar_url?: string;
  role: UserRole;
  salon_id?: string;
  created_at: string;
  updated_at: string;
}

export interface UserProfile extends User {
  bio?: string;
  specialties?: string[];
  certifications?: string[];
  preferred_brands?: string[];
  settings?: UserSettings;
}

export interface UserSettings {
  units: 'metric' | 'imperial';
  language: 'es' | 'en';
  notifications: NotificationSettings;
  calendar_preferences?: CalendarPreferences;
}

export interface NotificationSettings {
  appointments: boolean;
  inventory_alerts: boolean;
  client_messages: boolean;
  marketing: boolean;
}

export interface CalendarPreferences {
  default_view: 'day' | 'week' | 'month';
  working_hours: WorkingHours;
  buffer_time: number; // minutes between appointments
}

export interface WorkingHours {
  [key: string]: { // day of week
    start: string; // HH:MM
    end: string; // HH:MM
    breaks?: TimeSlot[];
  };
}

export interface TimeSlot {
  start: string;
  end: string;
}