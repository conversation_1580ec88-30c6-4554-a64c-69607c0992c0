export interface Database {
  public: {
    Tables: {
      users: {
        Row: User;
        Insert: Omit<User, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<User, 'id'>>;
      };
      salons: {
        Row: Salon;
        Insert: Omit<Salon, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Salon, 'id'>>;
      };
      clients: {
        Row: Client;
        Insert: Omit<Client, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Client, 'id'>>;
      };
      appointments: {
        Row: Appointment;
        Insert: Omit<Appointment, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Appointment, 'id'>>;
      };
      consultations: {
        Row: Consultation;
        Insert: Omit<Consultation, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Consultation, 'id'>>;
      };
      services: {
        Row: Service;
        Insert: Omit<Service, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Service, 'id'>>;
      };
      products: {
        Row: Product;
        Insert: Omit<Product, 'id' | 'created_at'>;
        Update: Partial<Omit<Product, 'id'>>;
      };
    };
    Views: {
      appointment_calendar: {
        Row: AppointmentCalendarView;
      };
      client_history: {
        Row: ClientHistoryView;
      };
      inventory_status: {
        Row: InventoryStatusView;
      };
    };
    Functions: {
      calculate_service_price: {
        Args: { service_id: string; factors: Record<string, any> };
        Returns: number;
      };
      get_available_slots: {
        Args: { stylist_id: string; date: string; duration: number };
        Returns: TimeSlot[];
      };
      generate_analytics_report: {
        Args: { salon_id: string; period: string; start_date: string; end_date: string };
        Returns: DashboardMetrics;
      };
    };
  };
}

export interface Salon {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  timezone: string;
  currency: string;
  subscription_plan: 'free' | 'basic' | 'premium' | 'enterprise';
  subscription_status: 'active' | 'cancelled' | 'past_due';
  created_at: string;
  updated_at: string;
}

export interface AppointmentCalendarView {
  appointment_id: string;
  client_name: string;
  stylist_name: string;
  service_name: string;
  start_time: string;
  end_time: string;
  status: string;
  price: number;
}

export interface ClientHistoryView {
  client_id: string;
  appointment_date: string;
  service_name: string;
  stylist_name: string;
  formula_used?: string;
  result_images?: string[];
  satisfaction_rating?: number;
}

export interface InventoryStatusView {
  product_id: string;
  product_name: string;
  current_stock: number;
  min_stock_level: number;
  stock_status: 'in_stock' | 'low_stock' | 'out_of_stock';
  last_used: string;
  usage_rate: number;
}

// Re-export types for convenience
export type { 
  User, UserRole, UserProfile,
  Client, ClientHairProfile, ClientAllergy,
  Appointment, AppointmentStatus,
  Consultation, HairDiagnosis, ColorFormulation,
  Service, ServiceCategory,
  Product, Brand, ProductLine,
  DashboardMetrics
} from './index';