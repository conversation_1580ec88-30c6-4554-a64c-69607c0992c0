export interface Brand {
  id: string;
  name: string;
  logo_url?: string;
  website?: string;
  is_professional: boolean;
}

export interface ProductLine {
  id: string;
  brand_id: string;
  name: string;
  category: 'color' | 'developer' | 'treatment' | 'styling';
  description?: string;
}

export interface Product {
  id: string;
  line_id: string;
  sku: string;
  name: string;
  shade_code?: string;
  shade_name?: string;
  size: number;
  unit: 'grams' | 'ml' | 'oz';
  cost_price?: number;
  retail_price?: number;
  is_active: boolean;
  allergens?: string[];
  created_at: string;
}

export interface SalonInventory {
  id: string;
  salon_id: string;
  product_id: string;
  current_stock: number;
  min_stock_level: number;
  max_stock_level: number;
  location?: string;
  last_restocked?: string;
  updated_at: string;
}

export interface InventoryMovement {
  id: string;
  salon_id: string;
  product_id: string;
  type: MovementType;
  quantity: number;
  reference_type?: 'consultation' | 'adjustment' | 'purchase' | 'return';
  reference_id?: string;
  notes?: string;
  created_at: string;
  created_by: string;
}

export type MovementType = 'in' | 'out' | 'adjustment';

export interface BrandConversion {
  id: string;
  from_product_id: string;
  to_product_id: string;
  conversion_notes?: string;
  confidence_level: 'high' | 'medium' | 'low';
  verified_by?: string;
  created_at: string;
}