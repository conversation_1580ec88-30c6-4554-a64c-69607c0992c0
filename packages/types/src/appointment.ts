export interface Appointment {
  id: string;
  salon_id: string;
  client_id: string;
  stylist_id: string;
  service_id: string;
  start_time: string;
  end_time: string;
  status: AppointmentStatus;
  notes?: string;
  price?: number;
  deposit_amount?: number;
  deposit_paid?: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
}

export type AppointmentStatus = 
  | 'scheduled'
  | 'confirmed'
  | 'in_progress'
  | 'completed'
  | 'cancelled'
  | 'no_show';

export interface AppointmentReminder {
  id: string;
  appointment_id: string;
  type: 'sms' | 'email' | 'push';
  scheduled_for: string;
  sent_at?: string;
  status: 'pending' | 'sent' | 'failed';
}