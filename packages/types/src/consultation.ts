export interface Consultation {
  id: string;
  salon_id: string;
  client_id: string;
  stylist_id: string;
  appointment_id?: string;
  status: 'draft' | 'in_progress' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

export interface HairDiagnosis {
  id: string;
  consultation_id: string;
  images: DiagnosisImage[];
  ai_analysis?: AIAnalysisResult;
  stylist_assessment?: StylistAssessment;
  created_at: string;
}

export interface DiagnosisImage {
  id: string;
  url: string;
  zone: 'roots' | 'mids' | 'ends' | 'overall' | 'crown' | 'nape';
  is_face_blurred: boolean;
  metadata?: ImageMetadata;
}

export interface ImageMetadata {
  width: number;
  height: number;
  size_kb: number;
  quality_score?: number; // 0-100
}

export interface AIAnalysisResult {
  natural_level: number;
  undertone: string;
  gray_percentage: number;
  condition_score: number;
  recommendations: string[];
  concerns: string[];
  confidence_score: number;
  analysis_timestamp: string;
}

export interface StylistAssessment {
  agrees_with_ai: boolean;
  adjustments?: Partial<AIAnalysisResult>;
  additional_notes?: string;
}

export interface ColorFormulation {
  id: string;
  consultation_id: string;
  target_result: ColorTarget;
  formula: Formula;
  technique: ApplicationTechnique;
  estimated_cost: number;
  estimated_time: number; // minutes
  created_at: string;
  is_final: boolean;
}

export interface ColorTarget {
  level: number;
  tone: string;
  reflects?: string[];
  reference_images?: string[];
  client_approved: boolean;
}

export interface Formula {
  products: FormulationProduct[];
  developer_volume: number;
  mixing_ratio: string;
  total_amount_grams: number;
  processing_time: number; // minutes
  special_instructions?: string[];
}

export interface FormulationProduct {
  product_id: string;
  brand: string;
  line: string;
  shade_code: string;
  shade_name: string;
  amount_grams: number;
  percentage: number;
}

export interface ApplicationTechnique {
  method: 'full_head' | 'regrowth' | 'highlights' | 'balayage' | 'ombre' | 'color_melt' | 'custom';
  sections?: string[];
  tools_needed?: string[];
  steps?: ApplicationStep[];
}

export interface ApplicationStep {
  order: number;
  description: string;
  duration_minutes: number;
  products?: string[];
}

export interface ServiceResult {
  id: string;
  consultation_id: string;
  before_images: string[];
  after_images: string[];
  actual_formula_used?: Formula;
  actual_processing_time?: number;
  client_satisfaction?: 1 | 2 | 3 | 4 | 5;
  stylist_notes?: string;
  client_feedback?: string;
  follow_up_required?: boolean;
  next_service_recommendation?: string;
  created_at: string;
}