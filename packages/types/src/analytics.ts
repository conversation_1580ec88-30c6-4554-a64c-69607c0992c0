export interface DashboardMetrics {
  period: 'day' | 'week' | 'month' | 'year';
  start_date: string;
  end_date: string;
  revenue: RevenueMetrics;
  appointments: AppointmentMetrics;
  clients: ClientMetrics;
  inventory: InventoryMetrics;
  stylist_performance?: StylistMetrics[];
}

export interface RevenueMetrics {
  total: number;
  by_service_category: Record<string, number>;
  by_stylist: Record<string, number>;
  average_ticket: number;
  growth_percentage: number;
  top_services: ServiceRevenue[];
}

export interface ServiceRevenue {
  service_id: string;
  service_name: string;
  count: number;
  total_revenue: number;
  average_price: number;
  profit_margin: number;
}

export interface AppointmentMetrics {
  total_count: number;
  completed_count: number;
  cancelled_count: number;
  no_show_count: number;
  average_duration: number;
  occupancy_rate: number;
  peak_hours: string[];
}

export interface ClientMetrics {
  total_clients: number;
  new_clients: number;
  returning_clients: number;
  retention_rate: number;
  average_visit_frequency: number;
  top_spenders: ClientSpending[];
}

export interface ClientSpending {
  client_id: string;
  client_name: string;
  total_spent: number;
  visit_count: number;
  average_spend: number;
  last_visit: string;
}

export interface InventoryMetrics {
  total_value: number;
  low_stock_items: number;
  out_of_stock_items: number;
  usage_by_category: Record<string, number>;
  cost_of_goods_sold: number;
  waste_percentage: number;
}

export interface StylistMetrics {
  stylist_id: string;
  stylist_name: string;
  revenue: number;
  appointments: number;
  average_ticket: number;
  client_satisfaction: number;
  productivity_score: number;
  top_services: string[];
}