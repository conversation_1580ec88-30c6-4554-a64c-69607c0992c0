export interface Service {
  id: string;
  salon_id: string;
  name: string;
  description?: string;
  category: ServiceCategory;
  base_price: number;
  duration_minutes: number;
  is_active: boolean;
  requires_consultation: boolean;
  deposit_percentage?: number;
  created_at: string;
  updated_at: string;
}

export type ServiceCategory = 
  | 'color'
  | 'cut'
  | 'treatment'
  | 'styling'
  | 'consultation'
  | 'package';

export interface ServicePricing {
  id: string;
  service_id: string;
  factor: PricingFactor;
  adjustment_type: 'percentage' | 'fixed';
  adjustment_value: number;
  created_at: string;
}

export type PricingFactor = 
  | 'hair_length'
  | 'hair_density'
  | 'complexity'
  | 'time_of_day'
  | 'day_of_week';

export interface ServiceProduct {
  id: string;
  service_id: string;
  product_id: string;
  average_amount_used: number;
  unit: 'grams' | 'ml';
}