import OpenAI from 'openai';
import type { 
  HairDiagnosis, 
  AIAnalysisResult, 
  ColorFormulation,
  Formula,
  FormulationProduct 
} from '@salonier/types';

const openai = new OpenAI({
  apiKey: process.env.EXPO_PUBLIC_OPENAI_API_KEY,
});

export class AIService {
  static async analyzeHairImage(imageBase64: string): Promise<AIAnalysisResult> {
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content: `You are an expert hair colorist AI assistant. Analyze the hair image and provide detailed technical assessment.
            
            Return a JSON object with the following structure:
            {
              "natural_level": number (1-10),
              "undertone": "warm" | "neutral" | "cool" | "ash",
              "gray_percentage": number (0-100),
              "condition_score": number (1-10),
              "recommendations": string[],
              "concerns": string[],
              "confidence_score": number (0-100)
            }`
          },
          {
            role: "user",
            content: [
              {
                type: "text",
                text: "Analyze this hair image for color consultation"
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/jpeg;base64,${imageBase64}`,
                  detail: "high"
                }
              }
            ]
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 1000,
        temperature: 0.3
      });

      const result = JSON.parse(response.choices[0].message.content || '{}');
      
      return {
        ...result,
        analysis_timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('AI hair analysis error:', error);
      throw new Error('Failed to analyze hair image');
    }
  }

  static async generateColorFormula(
    diagnosis: HairDiagnosis,
    targetColor: string,
    preferredBrands: string[]
  ): Promise<Formula> {
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4-turbo",
        messages: [
          {
            role: "system",
            content: `You are an expert hair colorist specializing in color formulation. 
            Create precise color formulas considering the client's hair diagnosis and target result.
            
            Preferred brands: ${preferredBrands.join(', ')}
            
            Return a JSON object with the formula details including:
            - products (array of product details)
            - developer_volume
            - mixing_ratio
            - processing_time
            - special_instructions`
          },
          {
            role: "user",
            content: `Current hair: ${JSON.stringify(diagnosis.ai_analysis)}
            Target color: ${targetColor}
            
            Generate a professional color formula.`
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 1500,
        temperature: 0.4
      });

      const formula = JSON.parse(response.choices[0].message.content || '{}');
      
      // Calculate total amount
      const totalAmount = formula.products.reduce(
        (sum: number, p: FormulationProduct) => sum + p.amount_grams, 
        0
      );
      
      return {
        ...formula,
        total_amount_grams: totalAmount
      };
    } catch (error) {
      console.error('Formula generation error:', error);
      throw new Error('Failed to generate color formula');
    }
  }

  static async suggestCorrectiveSteps(
    currentColor: AIAnalysisResult,
    targetColor: string,
    previousAttempts?: ColorFormulation[]
  ): Promise<string[]> {
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4-turbo",
        messages: [
          {
            role: "system",
            content: "You are a master colorist specializing in color correction. Provide step-by-step corrective color recommendations."
          },
          {
            role: "user",
            content: `Current hair state: ${JSON.stringify(currentColor)}
            Desired result: ${targetColor}
            ${previousAttempts ? `Previous attempts: ${JSON.stringify(previousAttempts)}` : ''}
            
            Provide detailed corrective steps.`
          }
        ],
        max_tokens: 1000,
        temperature: 0.5
      });

      const content = response.choices[0].message.content || '';
      return content.split('\n').filter(line => line.trim().length > 0);
    } catch (error) {
      console.error('Corrective steps error:', error);
      throw new Error('Failed to generate corrective steps');
    }
  }

  static async convertBrandFormula(
    formula: Formula,
    fromBrand: string,
    toBrand: string
  ): Promise<Formula> {
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4-turbo",
        messages: [
          {
            role: "system",
            content: `You are an expert in hair color brand conversions. 
            Convert color formulas between different professional hair color brands while maintaining the same result.
            Consider brand-specific characteristics like base colors, undertones, and developer systems.`
          },
          {
            role: "user",
            content: `Convert this ${fromBrand} formula to ${toBrand}:
            ${JSON.stringify(formula)}
            
            Provide equivalent products and adjusted ratios.`
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 1000,
        temperature: 0.3
      });

      return JSON.parse(response.choices[0].message.content || '{}');
    } catch (error) {
      console.error('Brand conversion error:', error);
      throw new Error('Failed to convert brand formula');
    }
  }
}