import { supabase } from './supabase';
import type { User, UserProfile } from '@salonier/types';

export interface AuthCredentials {
  email: string;
  password: string;
}

export interface SignUpData extends AuthCredentials {
  full_name: string;
  phone?: string;
  role: 'owner' | 'stylist' | 'receptionist';
  salon_name?: string;
}

export class AuthService {
  static async signIn({ email, password }: AuthCredentials) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;
    return data;
  }

  static async signUp(signUpData: SignUpData) {
    const { email, password, full_name, phone, role, salon_name } = signUpData;

    // Create auth user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name,
          phone,
          role,
        },
      },
    });

    if (authError) throw authError;

    // If owner, create salon
    if (role === 'owner' && salon_name && authData.user) {
      const { error: salonError } = await supabase
        .from('salons')
        .insert({
          name: salon_name,
          owner_id: authData.user.id,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          currency: 'USD',
          subscription_plan: 'free',
          subscription_status: 'active',
        });

      if (salonError) throw salonError;
    }

    return authData;
  }

  static async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  }

  static async getSession() {
    const { data, error } = await supabase.auth.getSession();
    if (error) throw error;
    return data.session;
  }

  static async getCurrentUser(): Promise<User | null> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) return null;

    const { data: profile, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single();

    if (error) throw error;
    return profile;
  }

  static async updateProfile(updates: Partial<UserProfile>) {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) throw new Error('No authenticated user');

    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', user.id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });

    if (error) throw error;
  }

  static async updatePassword(newPassword: string) {
    const { error } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (error) throw error;
  }
}