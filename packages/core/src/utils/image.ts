import * as FileSystem from 'expo-file-system';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';

export interface ImageProcessingOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: SaveFormat;
}

export class ImageUtils {
  static async processImageForAI(
    imageUri: string,
    options: ImageProcessingOptions = {}
  ): Promise<string> {
    const {
      maxWidth = 1024,
      maxHeight = 1024,
      quality = 0.8,
      format = SaveFormat.JPEG
    } = options;

    try {
      // Resize and compress image
      const manipulatedImage = await manipulateAsync(
        imageUri,
        [{ resize: { width: maxWidth, height: maxHeight } }],
        { compress: quality, format }
      );

      // Convert to base64
      const base64 = await FileSystem.readAsStringAsync(manipulatedImage.uri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      return base64;
    } catch (error) {
      console.error('Image processing error:', error);
      throw new Error('Failed to process image');
    }
  }

  static async detectAndBlurFaces(imageUri: string): Promise<string> {
    // This would integrate with a face detection library
    // For now, returning the original image
    // In production, use expo-face-detector or similar
    return imageUri;
  }

  static calculateImageHash(base64: string): string {
    // Simple hash for caching purposes
    // In production, use proper SHA-256
    let hash = 0;
    for (let i = 0; i < base64.length; i++) {
      const char = base64.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }

  static async compressForStorage(
    imageUri: string,
    targetSizeKB: number = 500
  ): Promise<string> {
    let quality = 0.9;
    let compressedUri = imageUri;
    let attempts = 0;
    const maxAttempts = 5;

    while (attempts < maxAttempts) {
      const result = await manipulateAsync(
        compressedUri,
        [],
        { compress: quality, format: SaveFormat.JPEG }
      );

      const info = await FileSystem.getInfoAsync(result.uri);
      const sizeKB = (info.size || 0) / 1024;

      if (sizeKB <= targetSizeKB) {
        return result.uri;
      }

      quality -= 0.15;
      compressedUri = result.uri;
      attempts++;
    }

    return compressedUri;
  }

  static async uploadToSupabase(
    imageUri: string,
    bucket: string,
    path: string
  ): Promise<string> {
    try {
      const base64 = await FileSystem.readAsStringAsync(imageUri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      const { supabase } = await import('../lib/supabase');
      
      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(path, decode(base64), {
          contentType: 'image/jpeg',
          upsert: false
        });

      if (error) throw error;

      const { data: { publicUrl } } = supabase.storage
        .from(bucket)
        .getPublicUrl(data.path);

      return publicUrl;
    } catch (error) {
      console.error('Upload error:', error);
      throw new Error('Failed to upload image');
    }
  }
}

function decode(base64: string): ArrayBuffer {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes.buffer;
}