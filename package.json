{"name": "salonier", "version": "1.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev:mobile": "cd apps/mobile && npm start", "android": "cd apps/mobile && npm run android", "ios": "cd apps/mobile && npm run ios", "web": "cd apps/mobile && npm run web", "clean": "rm -rf node_modules apps/*/node_modules packages/*/node_modules", "install:all": "npm install", "lint": "eslint . --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.1.3", "eslint": "^8.57.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0"}}