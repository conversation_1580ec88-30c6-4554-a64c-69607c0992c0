{"name": "@salonier/mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "eslint . --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.0", "@salonier/core": "*", "@salonier/types": "*", "@salonier/ui": "*", "@supabase/supabase-js": "^2.45.0", "@tanstack/react-query": "^5.0.0", "expo": "~53.0.12", "expo-camera": "~16.0.0", "expo-file-system": "~18.0.0", "expo-image-manipulator": "~13.0.0", "expo-image-picker": "~16.0.0", "expo-linear-gradient": "~14.0.0", "expo-router": "~4.0.0", "expo-secure-store": "~14.0.0", "expo-splash-screen": "~0.29.0", "expo-status-bar": "~2.0.0", "nativewind": "^2.0.0", "react": "18.3.1", "react-native": "0.76.6", "react-native-gesture-handler": "~2.20.2", "react-native-safe-area-context": "4.14.0", "react-native-screens": "~4.4.0", "zustand": "^4.4.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.3.0", "babel-plugin-module-resolver": "^5.0.2", "typescript": "~5.8.3"}, "private": true}