{"name": "@salonier/mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "eslint . --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@salonier/core": "*", "@salonier/types": "*", "@salonier/ui": "*", "@supabase/supabase-js": "^2.45.0", "@tanstack/react-query": "^5.0.0", "expo": "~53.0.12", "expo-camera": "~16.1.8", "expo-file-system": "~18.1.10", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-router": "~5.1.0", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "nativewind": "^2.0.0", "react": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "^0.20.0", "zustand": "^4.4.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.3.0", "babel-plugin-module-resolver": "^5.0.2", "typescript": "~5.8.3"}, "private": true}