// Mock data para desarrollo sin backend
// Datos de prueba que simulan la respuesta de Supabase

export interface MockUser {
  id: string;
  email: string;
  full_name: string;
  phone?: string;
  avatar_url?: string;
  role: 'owner' | 'stylist' | 'receptionist';
  salon_id: string;
  bio?: string;
  specialties: string[];
  certifications: string[];
  preferred_brands: string[];
  created_at: string;
}

export interface MockSalon {
  id: string;
  name: string;
  owner_id: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  timezone: string;
  currency: string;
  subscription_plan: 'free' | 'basic' | 'premium' | 'enterprise';
  created_at: string;
}

export interface MockClient {
  id: string;
  salon_id: string;
  full_name: string;
  email?: string;
  phone: string;
  date_of_birth?: string;
  notes?: string;
  tags: string[];
  preferred_stylist_id?: string;
  created_at: string;
  hair_profile?: MockHairProfile;
  last_visit?: string;
  total_visits: number;
  total_spent: number;
}

export interface MockHairProfile {
  id: string;
  client_id: string;
  natural_level: number; // 1-10
  undertone: 'warm' | 'neutral' | 'cool' | 'ash';
  gray_percentage: number; // 0-100
  hair_diameter: 'fine' | 'medium' | 'coarse';
  hair_density: 'thin' | 'medium' | 'thick';
  porosity: 'low' | 'medium' | 'high';
  elasticity: 'low' | 'medium' | 'high';
  condition: 'excellent' | 'good' | 'fair' | 'poor';
  scalp_condition?: string;
  chemical_history: Array<{
    date: string;
    service: string;
    products_used: string[];
    result: string;
  }>;
  updated_at: string;
}

export interface MockAppointment {
  id: string;
  salon_id: string;
  client_id: string;
  stylist_id: string;
  service_id: string;
  start_time: string;
  end_time: string;
  status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show';
  notes?: string;
  price: number;
  deposit_amount?: number;
  deposit_paid: boolean;
  created_at: string;
  // Datos relacionados para facilitar el desarrollo
  client_name: string;
  stylist_name: string;
  service_name: string;
}

export interface MockService {
  id: string;
  salon_id: string;
  name: string;
  description?: string;
  category: 'color' | 'cut' | 'treatment' | 'styling' | 'consultation' | 'package';
  base_price: number;
  duration_minutes: number;
  is_active: boolean;
  requires_consultation: boolean;
  deposit_percentage?: number;
  created_at: string;
}

// Datos mock del salón principal
export const mockSalon: MockSalon = {
  id: 'salon-1',
  name: 'Salón Belleza Total',
  owner_id: 'user-1',
  address: 'Calle Principal 123, Madrid, España',
  phone: '+34 91 123 4567',
  email: '<EMAIL>',
  website: 'www.bellezatotal.com',
  timezone: 'Europe/Madrid',
  currency: 'EUR',
  subscription_plan: 'premium',
  created_at: '2024-01-15T10:00:00Z',
};

// Usuarios mock
export const mockUsers: MockUser[] = [
  {
    id: 'user-1',
    email: '<EMAIL>',
    full_name: 'María García',
    phone: '+34 600 123 456',
    avatar_url: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
    role: 'owner',
    salon_id: 'salon-1',
    bio: 'Propietaria y estilista senior con 15 años de experiencia en coloración.',
    specialties: ['Coloración avanzada', 'Mechas', 'Corrección de color'],
    certifications: ['L\'Oréal Professional', 'Wella Professionals'],
    preferred_brands: ['L\'Oréal', 'Wella', 'Schwarzkopf'],
    created_at: '2024-01-15T10:00:00Z',
  },
  {
    id: 'user-2',
    email: '<EMAIL>',
    full_name: 'Ana Martínez',
    phone: '+34 600 234 567',
    avatar_url: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
    role: 'stylist',
    salon_id: 'salon-1',
    bio: 'Especialista en cortes modernos y tratamientos capilares.',
    specialties: ['Cortes', 'Tratamientos', 'Peinados'],
    certifications: ['Kerastase', 'Olaplex'],
    preferred_brands: ['Kerastase', 'Olaplex', 'Moroccanoil'],
    created_at: '2024-01-20T10:00:00Z',
  },
  {
    id: 'user-3',
    email: '<EMAIL>',
    full_name: 'Carlos López',
    phone: '+34 600 345 678',
    avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
    role: 'stylist',
    salon_id: 'salon-1',
    bio: 'Colorista experto en técnicas de balayage y color fantasía.',
    specialties: ['Balayage', 'Color fantasía', 'Decoloración'],
    certifications: ['Redken', 'Matrix'],
    preferred_brands: ['Redken', 'Matrix', 'Pravana'],
    created_at: '2024-02-01T10:00:00Z',
  },
];

// Servicios mock
export const mockServices: MockService[] = [
  {
    id: 'service-1',
    salon_id: 'salon-1',
    name: 'Coloración completa',
    description: 'Coloración de raíces y medios con productos profesionales',
    category: 'color',
    base_price: 85.00,
    duration_minutes: 120,
    is_active: true,
    requires_consultation: true,
    deposit_percentage: 30,
    created_at: '2024-01-15T10:00:00Z',
  },
  {
    id: 'service-2',
    salon_id: 'salon-1',
    name: 'Mechas tradicionales',
    description: 'Mechas con gorro o papel de aluminio',
    category: 'color',
    base_price: 95.00,
    duration_minutes: 150,
    is_active: true,
    requires_consultation: true,
    deposit_percentage: 30,
    created_at: '2024-01-15T10:00:00Z',
  },
  {
    id: 'service-3',
    salon_id: 'salon-1',
    name: 'Balayage',
    description: 'Técnica de iluminación natural pintada a mano',
    category: 'color',
    base_price: 120.00,
    duration_minutes: 180,
    is_active: true,
    requires_consultation: true,
    deposit_percentage: 40,
    created_at: '2024-01-15T10:00:00Z',
  },
  {
    id: 'service-4',
    salon_id: 'salon-1',
    name: 'Corte y peinado',
    description: 'Corte personalizado con acabado profesional',
    category: 'cut',
    base_price: 35.00,
    duration_minutes: 60,
    is_active: true,
    requires_consultation: false,
    created_at: '2024-01-15T10:00:00Z',
  },
  {
    id: 'service-5',
    salon_id: 'salon-1',
    name: 'Tratamiento reparador',
    description: 'Tratamiento intensivo para cabello dañado',
    category: 'treatment',
    base_price: 45.00,
    duration_minutes: 90,
    is_active: true,
    requires_consultation: false,
    created_at: '2024-01-15T10:00:00Z',
  },
  {
    id: 'service-6',
    salon_id: 'salon-1',
    name: 'Consulta de color',
    description: 'Análisis capilar y recomendación de color personalizada',
    category: 'consultation',
    base_price: 25.00,
    duration_minutes: 30,
    is_active: true,
    requires_consultation: false,
    created_at: '2024-01-15T10:00:00Z',
  },
];

// Perfiles capilares mock
export const mockHairProfiles: MockHairProfile[] = [
  {
    id: 'hair-profile-1',
    client_id: 'client-1',
    natural_level: 6,
    undertone: 'warm',
    gray_percentage: 15,
    hair_diameter: 'medium',
    hair_density: 'medium',
    porosity: 'medium',
    elasticity: 'medium',
    condition: 'good',
    scalp_condition: 'Normal, sin sensibilidad',
    chemical_history: [
      {
        date: '2024-05-15',
        service: 'Coloración completa',
        products_used: ['L\'Oréal Excellence 7.3', 'Oxidante 20vol'],
        result: 'Excelente cobertura, cliente satisfecha'
      },
      {
        date: '2024-03-10',
        service: 'Mechas',
        products_used: ['Decolorante Blondor', 'Tóner T18'],
        result: 'Mechas uniformes, buen contraste'
      }
    ],
    updated_at: '2024-06-20T10:00:00Z',
  },
  {
    id: 'hair-profile-2',
    client_id: 'client-2',
    natural_level: 4,
    undertone: 'cool',
    gray_percentage: 5,
    hair_diameter: 'fine',
    hair_density: 'thin',
    porosity: 'high',
    elasticity: 'low',
    condition: 'fair',
    scalp_condition: 'Sensible, tendencia a irritación',
    chemical_history: [
      {
        date: '2024-04-20',
        service: 'Balayage',
        products_used: ['Wella Blondor', 'Tóner T14'],
        result: 'Resultado natural, cliente muy contenta'
      }
    ],
    updated_at: '2024-06-18T10:00:00Z',
  },
];

// Clientes mock
export const mockClients: MockClient[] = [
  {
    id: 'client-1',
    salon_id: 'salon-1',
    full_name: 'Laura Fernández',
    email: '<EMAIL>',
    phone: '+34 666 111 222',
    date_of_birth: '1985-03-15',
    notes: 'Prefiere citas por la mañana. Alérgica al amoníaco.',
    tags: ['VIP', 'Alergia amoníaco'],
    preferred_stylist_id: 'user-1',
    created_at: '2024-02-10T10:00:00Z',
    hair_profile: mockHairProfiles[0],
    last_visit: '2024-06-20T10:00:00Z',
    total_visits: 8,
    total_spent: 680.00,
  },
  {
    id: 'client-2',
    salon_id: 'salon-1',
    full_name: 'Carmen Ruiz',
    email: '<EMAIL>',
    phone: '+34 666 222 333',
    date_of_birth: '1992-07-22',
    notes: 'Primera vez con color. Muy nerviosa, necesita explicaciones detalladas.',
    tags: ['Primera vez', 'Nerviosa'],
    preferred_stylist_id: 'user-3',
    created_at: '2024-03-05T10:00:00Z',
    hair_profile: mockHairProfiles[1],
    last_visit: '2024-06-18T10:00:00Z',
    total_visits: 3,
    total_spent: 285.00,
  },
  {
    id: 'client-3',
    salon_id: 'salon-1',
    full_name: 'Isabel Moreno',
    email: '<EMAIL>',
    phone: '+34 666 333 444',
    date_of_birth: '1978-11-08',
    notes: 'Cliente habitual, siempre puntual. Le gusta probar cosas nuevas.',
    tags: ['Habitual', 'Aventurera'],
    preferred_stylist_id: 'user-1',
    created_at: '2024-01-20T10:00:00Z',
    last_visit: '2024-06-15T10:00:00Z',
    total_visits: 12,
    total_spent: 1240.00,
  },
  {
    id: 'client-4',
    salon_id: 'salon-1',
    full_name: 'Sofía González',
    email: '<EMAIL>',
    phone: '+34 666 444 555',
    date_of_birth: '1995-05-30',
    notes: 'Estudiante, busca precios económicos. Muy activa en redes sociales.',
    tags: ['Estudiante', 'Redes sociales'],
    preferred_stylist_id: 'user-2',
    created_at: '2024-04-12T10:00:00Z',
    last_visit: '2024-06-10T10:00:00Z',
    total_visits: 4,
    total_spent: 180.00,
  },
  {
    id: 'client-5',
    salon_id: 'salon-1',
    full_name: 'Pilar Jiménez',
    email: '<EMAIL>',
    phone: '+34 666 555 666',
    date_of_birth: '1965-12-03',
    notes: 'Necesita cobertura de canas. Prefiere colores naturales.',
    tags: ['Canas', 'Natural'],
    preferred_stylist_id: 'user-1',
    created_at: '2024-01-30T10:00:00Z',
    last_visit: '2024-06-05T10:00:00Z',
    total_visits: 15,
    total_spent: 1575.00,
  },
];
