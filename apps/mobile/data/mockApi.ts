// Mock API functions que simulan las llamadas a Supabase
// Estas funciones devuelven promesas para simular llamadas asíncronas reales

import { 
  mockUsers, 
  mockClients, 
  mockServices, 
  mockSalon,
  MockUser, 
  MockClient, 
  MockService, 
  MockSalon 
} from './mockData';
import { 
  mockAppointments, 
  getTodayAppointments, 
  getUpcomingAppointments, 
  getClientAppointments,
  getStylistAppointments,
  MockAppointment 
} from './mockAppointments';

// Simular delay de red
const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms));

// ============ AUTH API ============
export const mockAuthApi = {
  // Simular login
  signIn: async (email: string, password: string): Promise<{ user: MockUser; error?: string }> => {
    await delay(800); // Simular tiempo de autenticación
    
    const user = mockUsers.find(u => u.email === email);
    
    if (!user) {
      return { user: null as any, error: 'Usuario no encontrado' };
    }
    
    // En un caso real, verificaríamos la contraseña
    if (password.length < 6) {
      return { user: null as any, error: 'Contraseña incorrecta' };
    }
    
    return { user };
  },

  // Simular registro
  signUp: async (email: string, password: string, fullName: string): Promise<{ user: MockUser; error?: string }> => {
    await delay(1000);
    
    // Verificar si el usuario ya existe
    const existingUser = mockUsers.find(u => u.email === email);
    if (existingUser) {
      return { user: null as any, error: 'El usuario ya existe' };
    }
    
    // Crear nuevo usuario mock
    const newUser: MockUser = {
      id: `user-${Date.now()}`,
      email,
      full_name: fullName,
      role: 'stylist', // Por defecto
      salon_id: 'salon-1', // Por defecto al salón principal
      specialties: [],
      certifications: [],
      preferred_brands: [],
      created_at: new Date().toISOString(),
    };
    
    // En una app real, esto se guardaría en la base de datos
    mockUsers.push(newUser);
    
    return { user: newUser };
  },

  // Simular logout
  signOut: async (): Promise<{ error?: string }> => {
    await delay(300);
    return {};
  },

  // Obtener usuario actual
  getCurrentUser: async (): Promise<{ user: MockUser | null }> => {
    await delay(200);
    // En una app real, esto vendría del token JWT
    return { user: mockUsers[0] }; // Devolver el primer usuario como ejemplo
  },
};

// ============ CLIENTS API ============
export const mockClientsApi = {
  // Obtener todos los clientes
  getClients: async (): Promise<MockClient[]> => {
    await delay(600);
    return mockClients;
  },

  // Obtener cliente por ID
  getClient: async (id: string): Promise<MockClient | null> => {
    await delay(300);
    return mockClients.find(client => client.id === id) || null;
  },

  // Buscar clientes
  searchClients: async (query: string): Promise<MockClient[]> => {
    await delay(400);
    const lowercaseQuery = query.toLowerCase();
    return mockClients.filter(client => 
      client.full_name.toLowerCase().includes(lowercaseQuery) ||
      client.phone.includes(query) ||
      client.email?.toLowerCase().includes(lowercaseQuery)
    );
  },

  // Crear nuevo cliente
  createClient: async (clientData: Partial<MockClient>): Promise<MockClient> => {
    await delay(800);
    
    const newClient: MockClient = {
      id: `client-${Date.now()}`,
      salon_id: 'salon-1',
      full_name: clientData.full_name || '',
      email: clientData.email,
      phone: clientData.phone || '',
      date_of_birth: clientData.date_of_birth,
      notes: clientData.notes,
      tags: clientData.tags || [],
      preferred_stylist_id: clientData.preferred_stylist_id,
      created_at: new Date().toISOString(),
      total_visits: 0,
      total_spent: 0,
    };
    
    mockClients.push(newClient);
    return newClient;
  },

  // Actualizar cliente
  updateClient: async (id: string, updates: Partial<MockClient>): Promise<MockClient | null> => {
    await delay(600);
    
    const clientIndex = mockClients.findIndex(client => client.id === id);
    if (clientIndex === -1) return null;
    
    mockClients[clientIndex] = { ...mockClients[clientIndex], ...updates };
    return mockClients[clientIndex];
  },
};

// ============ APPOINTMENTS API ============
export const mockAppointmentsApi = {
  // Obtener citas de hoy
  getTodayAppointments: async (): Promise<MockAppointment[]> => {
    await delay(400);
    return getTodayAppointments();
  },

  // Obtener próximas citas
  getUpcomingAppointments: async (days: number = 7): Promise<MockAppointment[]> => {
    await delay(500);
    return getUpcomingAppointments(days);
  },

  // Obtener todas las citas
  getAllAppointments: async (): Promise<MockAppointment[]> => {
    await delay(700);
    return mockAppointments;
  },

  // Obtener citas de un cliente
  getClientAppointments: async (clientId: string): Promise<MockAppointment[]> => {
    await delay(400);
    return getClientAppointments(clientId);
  },

  // Obtener citas de un estilista
  getStylistAppointments: async (stylistId: string, date?: Date): Promise<MockAppointment[]> => {
    await delay(400);
    return getStylistAppointments(stylistId, date);
  },

  // Crear nueva cita
  createAppointment: async (appointmentData: Partial<MockAppointment>): Promise<MockAppointment> => {
    await delay(800);
    
    const client = mockClients.find(c => c.id === appointmentData.client_id);
    const stylist = mockUsers.find(u => u.id === appointmentData.stylist_id);
    const service = mockServices.find(s => s.id === appointmentData.service_id);
    
    const newAppointment: MockAppointment = {
      id: `appointment-${Date.now()}`,
      salon_id: 'salon-1',
      client_id: appointmentData.client_id || '',
      stylist_id: appointmentData.stylist_id || '',
      service_id: appointmentData.service_id || '',
      start_time: appointmentData.start_time || new Date().toISOString(),
      end_time: appointmentData.end_time || new Date().toISOString(),
      status: appointmentData.status || 'scheduled',
      notes: appointmentData.notes,
      price: appointmentData.price || service?.base_price || 0,
      deposit_amount: appointmentData.deposit_amount,
      deposit_paid: appointmentData.deposit_paid || false,
      created_at: new Date().toISOString(),
      client_name: client?.full_name || 'Cliente desconocido',
      stylist_name: stylist?.full_name || 'Estilista desconocido',
      service_name: service?.name || 'Servicio desconocido',
    };
    
    mockAppointments.push(newAppointment);
    return newAppointment;
  },

  // Actualizar cita
  updateAppointment: async (id: string, updates: Partial<MockAppointment>): Promise<MockAppointment | null> => {
    await delay(600);
    
    const appointmentIndex = mockAppointments.findIndex(apt => apt.id === id);
    if (appointmentIndex === -1) return null;
    
    mockAppointments[appointmentIndex] = { ...mockAppointments[appointmentIndex], ...updates };
    return mockAppointments[appointmentIndex];
  },
};

// ============ SERVICES API ============
export const mockServicesApi = {
  // Obtener todos los servicios
  getServices: async (): Promise<MockService[]> => {
    await delay(400);
    return mockServices.filter(service => service.is_active);
  },

  // Obtener servicios por categoría
  getServicesByCategory: async (category: string): Promise<MockService[]> => {
    await delay(300);
    return mockServices.filter(service => service.category === category && service.is_active);
  },
};

// ============ SALON API ============
export const mockSalonApi = {
  // Obtener información del salón
  getSalon: async (): Promise<MockSalon> => {
    await delay(300);
    return mockSalon;
  },

  // Obtener estadísticas del salón
  getSalonStats: async (): Promise<{
    totalClients: number;
    todayAppointments: number;
    monthlyRevenue: number;
    averageRating: number;
  }> => {
    await delay(600);
    
    const todayAppointments = getTodayAppointments();
    const completedAppointments = mockAppointments.filter(apt => apt.status === 'completed');
    
    return {
      totalClients: mockClients.length,
      todayAppointments: todayAppointments.length,
      monthlyRevenue: completedAppointments.reduce((sum, apt) => sum + apt.price, 0),
      averageRating: 4.8, // Valor fijo para el mock
    };
  },
};

// ============ USERS API ============
export const mockUsersApi = {
  // Obtener todos los usuarios/estilistas
  getUsers: async (): Promise<MockUser[]> => {
    await delay(400);
    return mockUsers;
  },

  // Obtener estilistas
  getStylists: async (): Promise<MockUser[]> => {
    await delay(300);
    return mockUsers.filter(user => user.role === 'stylist' || user.role === 'owner');
  },
};
