{"expo": {"name": "<PERSON><PERSON>", "slug": "salonier", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#0284c7"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.salonier.app", "infoPlist": {"NSCameraUsageDescription": "This app needs access to camera to capture hair images for color analysis.", "NSPhotoLibraryUsageDescription": "This app needs access to photo library to select hair images for color analysis."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#0284c7"}, "edgeToEdgeEnabled": true, "package": "com.salonier.app", "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-router", "expo-secure-store", "expo-camera", "expo-image-picker"], "extra": {"router": {"origin": false}, "eas": {"projectId": "your-project-id"}}}}