module.exports = function(api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      'nativewind/babel',
      [
        'module-resolver',
        {
          alias: {
            '@salonier/ui': '../../packages/ui/src',
            '@salonier/core': '../../packages/core/src',
            '@salonier/types': '../../packages/types/src',
          },
        },
      ],
    ],
  };
};