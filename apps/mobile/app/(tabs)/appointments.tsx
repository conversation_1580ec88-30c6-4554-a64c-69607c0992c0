import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { getTodayAppointments, getUpcomingAppointments } from '../../data/mockAppointments';

export default function AppointmentsScreen() {
  const { theme } = useTheme();
  const todayAppointments = getTodayAppointments();
  const upcomingAppointments = getUpcomingAppointments(7);

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('es-ES', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', { 
      weekday: 'long',
      day: 'numeric',
      month: 'long'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return theme.success;
      case 'scheduled': return theme.warning;
      case 'in_progress': return theme.info;
      case 'completed': return theme.primary;
      case 'cancelled': return theme.error;
      default: return theme.textSecondary;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'confirmed': return 'Confirmada';
      case 'scheduled': return 'Programada';
      case 'in_progress': return 'En progreso';
      case 'completed': return 'Completada';
      case 'cancelled': return 'Cancelada';
      default: return status;
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={styles.content}>
        {/* Citas de hoy */}
        <View style={[styles.section, { backgroundColor: theme.surface, borderColor: theme.border }]}>
          <Text style={[styles.sectionTitle, { color: theme.text }]}>
            Citas de hoy ({todayAppointments.length})
          </Text>
          
          {todayAppointments.length === 0 ? (
            <Text style={[styles.emptyText, { color: theme.textSecondary }]}>
              No hay citas programadas para hoy
            </Text>
          ) : (
            todayAppointments.map((appointment) => (
              <View key={appointment.id} style={[styles.appointmentCard, { borderColor: theme.border }]}>
                <View style={styles.appointmentHeader}>
                  <Text style={[styles.timeText, { color: theme.primary }]}>
                    {formatTime(appointment.start_time)}
                  </Text>
                  <View style={[styles.statusBadge, { backgroundColor: getStatusColor(appointment.status) + '20' }]}>
                    <Text style={[styles.statusText, { color: getStatusColor(appointment.status) }]}>
                      {getStatusText(appointment.status)}
                    </Text>
                  </View>
                </View>
                
                <Text style={[styles.clientName, { color: theme.text }]}>
                  {appointment.client_name}
                </Text>
                <Text style={[styles.serviceText, { color: theme.textSecondary }]}>
                  {appointment.service_name}
                </Text>
                <Text style={[styles.stylistText, { color: theme.textSecondary }]}>
                  Con {appointment.stylist_name}
                </Text>
                <Text style={[styles.priceText, { color: theme.success }]}>
                  €{appointment.price.toFixed(2)}
                </Text>
              </View>
            ))
          )}
        </View>

        {/* Próximas citas */}
        <View style={[styles.section, { backgroundColor: theme.surface, borderColor: theme.border }]}>
          <Text style={[styles.sectionTitle, { color: theme.text }]}>
            Próximas citas (7 días)
          </Text>
          
          {upcomingAppointments.length === 0 ? (
            <Text style={[styles.emptyText, { color: theme.textSecondary }]}>
              No hay citas programadas
            </Text>
          ) : (
            upcomingAppointments.map((appointment) => (
              <View key={appointment.id} style={[styles.appointmentCard, { borderColor: theme.border }]}>
                <View style={styles.appointmentHeader}>
                  <Text style={[styles.dateText, { color: theme.primary }]}>
                    {formatDate(appointment.start_time)}
                  </Text>
                  <Text style={[styles.timeText, { color: theme.primary }]}>
                    {formatTime(appointment.start_time)}
                  </Text>
                </View>
                
                <Text style={[styles.clientName, { color: theme.text }]}>
                  {appointment.client_name}
                </Text>
                <Text style={[styles.serviceText, { color: theme.textSecondary }]}>
                  {appointment.service_name}
                </Text>
                <Text style={[styles.stylistText, { color: theme.textSecondary }]}>
                  Con {appointment.stylist_name}
                </Text>
              </View>
            ))
          )}
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  section: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: 20,
  },
  appointmentCard: {
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    marginBottom: 8,
  },
  appointmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  timeText: {
    fontSize: 16,
    fontWeight: '600',
  },
  dateText: {
    fontSize: 14,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  clientName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  serviceText: {
    fontSize: 14,
    marginBottom: 2,
  },
  stylistText: {
    fontSize: 12,
    marginBottom: 4,
  },
  priceText: {
    fontSize: 14,
    fontWeight: '600',
  },
});
