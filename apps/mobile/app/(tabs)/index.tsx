import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { router } from 'expo-router';
import { mockSalonApi, mockAppointmentsApi, mockClientsApi } from '../../data/mockApi';
import { getTodayAppointments } from '../../data/mockAppointments';

export default function DashboardScreen() {
  const { user, signOut } = useAuth();
  const { theme } = useTheme();
  const [stats, setStats] = useState({
    totalClients: 0,
    todayAppointments: 0,
    monthlyRevenue: 0,
    averageRating: 0,
  });
  const [todayAppointments, setTodayAppointments] = useState<any[]>([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      // Cargar estadísticas del salón
      const salonStats = await mockSalonApi.getSalonStats();
      setStats(salonStats);

      // Cargar citas de hoy
      const appointments = await mockAppointmentsApi.getTodayAppointments();
      setTodayAppointments(appointments.slice(0, 3)); // Mostrar solo las primeras 3
    } catch (error) {
      console.error('Error cargando datos del dashboard:', error);
    }
  };

  const handleSignOut = async () => {
    await signOut();
    router.replace('/(auth)/login');
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('es-ES', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={styles.content}>
        {/* Header de bienvenida */}
        <View style={[styles.welcomeCard, { backgroundColor: theme.surface, borderColor: theme.border }]}>
          <Text style={[styles.welcomeTitle, { color: theme.text }]}>
            ¡Hola, {user?.full_name}! 👋
          </Text>
          <Text style={[styles.welcomeSubtitle, { color: theme.textSecondary }]}>
            Bienvenido a Salonier
          </Text>
          <Text style={[styles.roleText, { color: theme.primary }]}>
            {user?.role === 'owner' ? 'Propietario' : 
             user?.role === 'stylist' ? 'Estilista' : 'Recepcionista'}
          </Text>
        </View>

        {/* Estadísticas rápidas */}
        <View style={styles.statsContainer}>
          <View style={[styles.statCard, { backgroundColor: theme.surface, borderColor: theme.border }]}>
            <Text style={[styles.statNumber, { color: theme.primary }]}>
              {stats.todayAppointments}
            </Text>
            <Text style={[styles.statLabel, { color: theme.textSecondary }]}>Citas hoy</Text>
          </View>

          <View style={[styles.statCard, { backgroundColor: theme.surface, borderColor: theme.border }]}>
            <Text style={[styles.statNumber, { color: theme.success }]}>
              {stats.totalClients}
            </Text>
            <Text style={[styles.statLabel, { color: theme.textSecondary }]}>Clientes</Text>
          </View>

          <View style={[styles.statCard, { backgroundColor: theme.surface, borderColor: theme.border }]}>
            <Text style={[styles.statNumber, { color: theme.gold }]}>
              €{stats.monthlyRevenue.toLocaleString()}
            </Text>
            <Text style={[styles.statLabel, { color: theme.textSecondary }]}>Este mes</Text>
          </View>
        </View>

        {/* Próximas citas */}
        <View style={[styles.section, { backgroundColor: theme.surface, borderColor: theme.border }]}>
          <Text style={[styles.sectionTitle, { color: theme.text }]}>
            Próximas citas ({todayAppointments.length})
          </Text>

          {todayAppointments.length === 0 ? (
            <Text style={[styles.emptyText, { color: theme.textSecondary }]}>
              No hay citas programadas para hoy
            </Text>
          ) : (
            todayAppointments.map((appointment) => (
              <View key={appointment.id} style={styles.appointmentItem}>
                <View style={styles.appointmentTime}>
                  <Text style={[styles.timeText, { color: theme.primary }]}>
                    {formatTime(appointment.start_time)}
                  </Text>
                </View>
                <View style={styles.appointmentDetails}>
                  <Text style={[styles.clientName, { color: theme.text }]}>
                    {appointment.client_name}
                  </Text>
                  <Text style={[styles.serviceText, { color: theme.textSecondary }]}>
                    {appointment.service_name}
                  </Text>
                  <Text style={[styles.priceText, { color: theme.success }]}>
                    €{appointment.price.toFixed(2)}
                  </Text>
                </View>
              </View>
            ))
          )}
        </View>

        {/* Acciones rápidas */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: theme.primary }]}
            onPress={() => router.push('/clients')}
          >
            <Text style={styles.actionButtonText}>Ver Clientes</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: theme.secondary }]}
            onPress={() => router.push('/appointments')}
          >
            <Text style={styles.actionButtonText}>Ver Citas</Text>
          </TouchableOpacity>
        </View>

        {/* Botón de cerrar sesión */}
        <TouchableOpacity 
          style={[styles.signOutButton, { borderColor: theme.error }]}
          onPress={handleSignOut}
        >
          <Text style={[styles.signOutText, { color: theme.error }]}>Cerrar Sesión</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  welcomeCard: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 20,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  welcomeSubtitle: {
    fontSize: 16,
    marginBottom: 8,
  },
  roleText: {
    fontSize: 14,
    fontWeight: '600',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  section: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  appointmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  appointmentTime: {
    width: 60,
    marginRight: 16,
  },
  timeText: {
    fontSize: 14,
    fontWeight: '600',
  },
  appointmentDetails: {
    flex: 1,
  },
  clientName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  serviceText: {
    fontSize: 14,
    marginBottom: 4,
  },
  priceText: {
    fontSize: 12,
    fontWeight: '600',
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: 20,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  actionButton: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  actionButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  signOutButton: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  signOutText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
