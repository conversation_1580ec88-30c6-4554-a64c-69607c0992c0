import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';

export default function DashboardScreen() {
  const handleSignOut = () => {
    router.replace('/(auth)/login');
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        {/* Header de bienvenida */}
        <View style={styles.welcomeCard}>
          <Text style={styles.welcomeTitle}>
            ¡Hola, María! 👋
          </Text>
          <Text style={styles.welcomeSubtitle}>
            Bienvenido a Salonier
          </Text>
          <Text style={styles.roleText}>
            Propietario
          </Text>
        </View>

        {/* Estadísticas rápidas */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>4</Text>
            <Text style={styles.statLabel}>Citas hoy</Text>
          </View>

          <View style={styles.statCard}>
            <Text style={styles.statNumber}>156</Text>
            <Text style={styles.statLabel}>Clientes</Text>
          </View>

          <View style={styles.statCard}>
            <Text style={styles.statNumber}>€2,450</Text>
            <Text style={styles.statLabel}>Este mes</Text>
          </View>
        </View>

        {/* Próximas citas */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Próximas citas (2)</Text>

          <View style={styles.appointmentItem}>
            <View style={styles.appointmentTime}>
              <Text style={styles.timeText}>09:00</Text>
            </View>
            <View style={styles.appointmentDetails}>
              <Text style={styles.clientName}>Laura Fernández</Text>
              <Text style={styles.serviceText}>Coloración completa</Text>
              <Text style={styles.priceText}>€85.00</Text>
            </View>
          </View>

          <View style={styles.appointmentItem}>
            <View style={styles.appointmentTime}>
              <Text style={styles.timeText}>11:30</Text>
            </View>
            <View style={styles.appointmentDetails}>
              <Text style={styles.clientName}>Carmen Ruiz</Text>
              <Text style={styles.serviceText}>Balayage</Text>
              <Text style={styles.priceText}>€120.00</Text>
            </View>
          </View>
        </View>

        {/* Acciones rápidas */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => router.push('/clients')}
          >
            <Text style={styles.actionButtonText}>Ver Clientes</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryButton]}
            onPress={() => router.push('/appointments')}
          >
            <Text style={styles.actionButtonText}>Ver Citas</Text>
          </TouchableOpacity>
        </View>

        {/* Botón de cerrar sesión */}
        <TouchableOpacity
          style={styles.signOutButton}
          onPress={handleSignOut}
        >
          <Text style={styles.signOutText}>Cerrar Sesión</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  content: {
    padding: 20,
  },
  welcomeCard: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 20,
    backgroundColor: '#f8fafc',
    borderColor: '#e5e7eb',
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#111827',
  },
  welcomeSubtitle: {
    fontSize: 16,
    marginBottom: 8,
    color: '#6b7280',
  },
  roleText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0284c7',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginHorizontal: 4,
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    borderColor: '#e5e7eb',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#0284c7',
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
    color: '#6b7280',
  },
  section: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 20,
    backgroundColor: '#f8fafc',
    borderColor: '#e5e7eb',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    color: '#111827',
  },
  appointmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  appointmentTime: {
    width: 60,
    marginRight: 16,
  },
  timeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0284c7',
  },
  appointmentDetails: {
    flex: 1,
  },
  clientName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
    color: '#111827',
  },
  serviceText: {
    fontSize: 14,
    marginBottom: 4,
    color: '#6b7280',
  },
  priceText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#10b981',
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: 20,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  actionButton: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
    backgroundColor: '#0284c7',
  },
  secondaryButton: {
    backgroundColor: '#f59e0b',
  },
  actionButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  signOutButton: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    borderColor: '#ef4444',
  },
  signOutText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ef4444',
  },
});
