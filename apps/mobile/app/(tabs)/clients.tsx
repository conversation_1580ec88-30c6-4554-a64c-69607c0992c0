import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { mockClients, MockClient } from '../../data/mockData';
import { mockClientsApi } from '../../data/mockApi';

export default function ClientsScreen() {
  const { theme } = useTheme();
  const [clients, setClients] = useState<MockClient[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredClients, setFilteredClients] = useState<MockClient[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadClients();
  }, []);

  useEffect(() => {
    filterClients();
  }, [clients, searchQuery]);

  const loadClients = async () => {
    try {
      setIsLoading(true);
      const clientsData = await mockClientsApi.getClients();
      setClients(clientsData);
    } catch (error) {
      console.error('Error cargando clientes:', error);
      Alert.alert('Error', 'No se pudieron cargar los clientes');
    } finally {
      setIsLoading(false);
    }
  };

  const filterClients = () => {
    if (!searchQuery.trim()) {
      setFilteredClients(clients);
      return;
    }

    const filtered = clients.filter(client =>
      client.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      client.phone.includes(searchQuery) ||
      client.email?.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredClients(filtered);
  };

  const handleClientPress = (client: MockClient) => {
    Alert.alert(
      client.full_name,
      `Teléfono: ${client.phone}\nEmail: ${client.email}\nVisitas: ${client.total_visits}\nGastado: €${client.total_spent.toFixed(2)}\n\nNotas: ${client.notes || 'Sin notas'}`,
      [{ text: 'Cerrar' }]
    );
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={styles.content}>
        <View style={[styles.header, { backgroundColor: theme.surface, borderColor: theme.border }]}>
          <Text style={[styles.title, { color: theme.text }]}>Clientes</Text>
          <Text style={[styles.subtitle, { color: theme.textSecondary }]}>
            {clients.length} clientes registrados
          </Text>
        </View>

        {/* Barra de búsqueda */}
        <View style={[styles.searchContainer, { backgroundColor: theme.surface, borderColor: theme.border }]}>
          <TextInput
            style={[styles.searchInput, { color: theme.text }]}
            placeholder="Buscar por nombre, teléfono o email..."
            placeholderTextColor={theme.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        {/* Lista de clientes */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <Text style={[styles.loadingText, { color: theme.textSecondary }]}>
              Cargando clientes...
            </Text>
          </View>
        ) : filteredClients.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, { color: theme.textSecondary }]}>
              {searchQuery ? 'No se encontraron clientes' : 'No hay clientes registrados'}
            </Text>
          </View>
        ) : (
          filteredClients.map((client) => (
            <TouchableOpacity
              key={client.id}
              style={[styles.clientCard, { backgroundColor: theme.surface, borderColor: theme.border }]}
              onPress={() => handleClientPress(client)}
            >
              <View style={styles.clientInfo}>
                <Text style={[styles.clientName, { color: theme.text }]}>
                  {client.full_name}
                </Text>
                <Text style={[styles.clientPhone, { color: theme.textSecondary }]}>
                  📞 {client.phone}
                </Text>
                <Text style={[styles.clientEmail, { color: theme.textSecondary }]}>
                  ✉️ {client.email}
                </Text>
                {client.tags && client.tags.length > 0 && (
                  <View style={styles.tagsContainer}>
                    {client.tags.slice(0, 2).map((tag, index) => (
                      <View key={index} style={[styles.tag, { backgroundColor: theme.primary + '20' }]}>
                        <Text style={[styles.tagText, { color: theme.primary }]}>{tag}</Text>
                      </View>
                    ))}
                  </View>
                )}
              </View>

              <View style={styles.clientStats}>
                <Text style={[styles.statText, { color: theme.primary }]}>
                  {client.total_visits} visitas
                </Text>
                <Text style={[styles.statText, { color: theme.success }]}>
                  €{client.total_spent.toFixed(2)}
                </Text>
                {client.last_visit && (
                  <Text style={[styles.lastVisitText, { color: theme.textSecondary }]}>
                    Última: {new Date(client.last_visit).toLocaleDateString('es-ES')}
                  </Text>
                )}
              </View>
            </TouchableOpacity>
          ))
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
  },
  clientCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 12,
  },
  clientInfo: {
    flex: 1,
  },
  clientName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  clientPhone: {
    fontSize: 14,
    marginBottom: 2,
  },
  clientEmail: {
    fontSize: 14,
  },
  clientStats: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  statText: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 2,
  },
  searchContainer: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 16,
  },
  searchInput: {
    fontSize: 16,
    paddingVertical: 8,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontStyle: 'italic',
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    fontStyle: 'italic',
    textAlign: 'center',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
    gap: 4,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  tagText: {
    fontSize: 10,
    fontWeight: '600',
  },
  lastVisitText: {
    fontSize: 10,
    marginTop: 4,
  },
});
