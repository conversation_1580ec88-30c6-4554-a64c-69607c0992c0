import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { mockClients } from '../../data/mockData';

export default function ClientsScreen() {
  const { theme } = useTheme();

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={styles.content}>
        <View style={[styles.header, { backgroundColor: theme.surface, borderColor: theme.border }]}>
          <Text style={[styles.title, { color: theme.text }]}>Clientes</Text>
          <Text style={[styles.subtitle, { color: theme.textSecondary }]}>
            {mockClients.length} clientes registrados
          </Text>
        </View>

        {mockClients.map((client) => (
          <TouchableOpacity 
            key={client.id}
            style={[styles.clientCard, { backgroundColor: theme.surface, borderColor: theme.border }]}
          >
            <View style={styles.clientInfo}>
              <Text style={[styles.clientName, { color: theme.text }]}>
                {client.full_name}
              </Text>
              <Text style={[styles.clientPhone, { color: theme.textSecondary }]}>
                {client.phone}
              </Text>
              <Text style={[styles.clientEmail, { color: theme.textSecondary }]}>
                {client.email}
              </Text>
            </View>
            
            <View style={styles.clientStats}>
              <Text style={[styles.statText, { color: theme.primary }]}>
                {client.total_visits} visitas
              </Text>
              <Text style={[styles.statText, { color: theme.success }]}>
                €{client.total_spent.toFixed(2)}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
  },
  clientCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 12,
  },
  clientInfo: {
    flex: 1,
  },
  clientName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  clientPhone: {
    fontSize: 14,
    marginBottom: 2,
  },
  clientEmail: {
    fontSize: 14,
  },
  clientStats: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  statText: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 2,
  },
});
