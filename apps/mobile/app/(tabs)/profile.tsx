import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { router } from 'expo-router';

export default function ProfileScreen() {
  const { user, signOut } = useAuth();
  const { theme, toggleTheme, isDark } = useTheme();

  const handleSignOut = async () => {
    Alert.alert(
      'Cerrar Sesión',
      '¿Estás seguro de que quieres cerrar sesión?',
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Cerrar Sesión', 
          style: 'destructive',
          onPress: async () => {
            await signOut();
            router.replace('/(auth)/login');
          }
        },
      ]
    );
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'owner': return 'Propietario';
      case 'stylist': return 'Estilista';
      case 'receptionist': return 'Recepcionista';
      default: return role;
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={styles.content}>
        {/* Header del perfil */}
        <View style={[styles.profileHeader, { backgroundColor: theme.surface, borderColor: theme.border }]}>
          <View style={[styles.avatar, { backgroundColor: theme.primary }]}>
            <Text style={styles.avatarText}>
              {user?.full_name?.charAt(0).toUpperCase() || 'U'}
            </Text>
          </View>
          
          <Text style={[styles.userName, { color: theme.text }]}>
            {user?.full_name || 'Usuario'}
          </Text>
          <Text style={[styles.userEmail, { color: theme.textSecondary }]}>
            {user?.email}
          </Text>
          <Text style={[styles.userRole, { color: theme.primary }]}>
            {getRoleDisplayName(user?.role || '')}
          </Text>
        </View>

        {/* Información profesional */}
        {user?.role !== 'receptionist' && (
          <View style={[styles.section, { backgroundColor: theme.surface, borderColor: theme.border }]}>
            <Text style={[styles.sectionTitle, { color: theme.text }]}>
              Información Profesional
            </Text>
            
            {user?.bio && (
              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: theme.textSecondary }]}>Biografía</Text>
                <Text style={[styles.infoValue, { color: theme.text }]}>{user.bio}</Text>
              </View>
            )}

            {user?.specialties && user.specialties.length > 0 && (
              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: theme.textSecondary }]}>Especialidades</Text>
                <View style={styles.tagsContainer}>
                  {user.specialties.map((specialty, index) => (
                    <View key={index} style={[styles.tag, { backgroundColor: theme.primary + '20' }]}>
                      <Text style={[styles.tagText, { color: theme.primary }]}>{specialty}</Text>
                    </View>
                  ))}
                </View>
              </View>
            )}

            {user?.certifications && user.certifications.length > 0 && (
              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: theme.textSecondary }]}>Certificaciones</Text>
                <View style={styles.tagsContainer}>
                  {user.certifications.map((cert, index) => (
                    <View key={index} style={[styles.tag, { backgroundColor: theme.success + '20' }]}>
                      <Text style={[styles.tagText, { color: theme.success }]}>{cert}</Text>
                    </View>
                  ))}
                </View>
              </View>
            )}

            {user?.preferred_brands && user.preferred_brands.length > 0 && (
              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: theme.textSecondary }]}>Marcas Preferidas</Text>
                <View style={styles.tagsContainer}>
                  {user.preferred_brands.map((brand, index) => (
                    <View key={index} style={[styles.tag, { backgroundColor: theme.gold + '20' }]}>
                      <Text style={[styles.tagText, { color: theme.gold }]}>{brand}</Text>
                    </View>
                  ))}
                </View>
              </View>
            )}
          </View>
        )}

        {/* Configuración */}
        <View style={[styles.section, { backgroundColor: theme.surface, borderColor: theme.border }]}>
          <Text style={[styles.sectionTitle, { color: theme.text }]}>
            Configuración
          </Text>
          
          <TouchableOpacity 
            style={styles.settingItem}
            onPress={toggleTheme}
          >
            <Text style={[styles.settingLabel, { color: theme.text }]}>
              Tema {isDark ? 'Oscuro' : 'Claro'}
            </Text>
            <Text style={[styles.settingValue, { color: theme.primary }]}>
              {isDark ? '🌙' : '☀️'}
            </Text>
          </TouchableOpacity>

          <View style={styles.settingItem}>
            <Text style={[styles.settingLabel, { color: theme.text }]}>
              Versión de la App
            </Text>
            <Text style={[styles.settingValue, { color: theme.textSecondary }]}>
              1.0.0 (Demo)
            </Text>
          </View>
        </View>

        {/* Estadísticas personales */}
        <View style={[styles.section, { backgroundColor: theme.surface, borderColor: theme.border }]}>
          <Text style={[styles.sectionTitle, { color: theme.text }]}>
            Estadísticas
          </Text>
          
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.primary }]}>24</Text>
              <Text style={[styles.statLabel, { color: theme.textSecondary }]}>Citas esta semana</Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.success }]}>98%</Text>
              <Text style={[styles.statLabel, { color: theme.textSecondary }]}>Satisfacción</Text>
            </View>
          </View>
        </View>

        {/* Acciones */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: theme.primary }]}
            onPress={() => Alert.alert('Próximamente', 'Función en desarrollo')}
          >
            <Text style={styles.actionButtonText}>Editar Perfil</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.signOutButton, { borderColor: theme.error }]}
            onPress={handleSignOut}
          >
            <Text style={[styles.signOutText, { color: theme.error }]}>Cerrar Sesión</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  profileHeader: {
    padding: 24,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
    marginBottom: 20,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatarText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 16,
    marginBottom: 8,
  },
  userRole: {
    fontSize: 14,
    fontWeight: '600',
  },
  section: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  infoItem: {
    marginBottom: 16,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  infoValue: {
    fontSize: 16,
    lineHeight: 22,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  tagText: {
    fontSize: 12,
    fontWeight: '600',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  settingLabel: {
    fontSize: 16,
  },
  settingValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  actionsContainer: {
    marginTop: 20,
  },
  actionButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  actionButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  signOutButton: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  signOutText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
