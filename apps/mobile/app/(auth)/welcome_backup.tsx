import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Link } from 'expo-router';
import { Button, colors, typography, spacing } from '@salonier/ui';
import { LinearGradient } from 'expo-linear-gradient';

export default function WelcomeScreen() {
  return (
    <LinearGradient
      colors={[colors.primary[700], colors.primary[900]]}
      style={styles.container}
    >
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.content}>
          <View style={styles.logoContainer}>
            <Text style={styles.logo}>Salonier</Text>
            <Text style={styles.tagline}>
              Elevate Your Color Expertise with AI
            </Text>
          </View>

          <View style={styles.illustration}>
            {/* Placeholder for illustration */}
            <View style={styles.illustrationPlaceholder} />
          </View>

          <View style={styles.footer}>
            <Link href="/(auth)/register" asChild>
              <Button variant="primary" size="lg" fullWidth>
                Get Started
              </Button>
            </Link>
            
            <Link href="/(auth)/login" asChild>
              <Button variant="outline" size="lg" fullWidth style={styles.loginButton}>
                I already have an account
              </Button>
            </Link>

            <Text style={styles.terms}>
              By continuing, you agree to our{' '}
              <Text style={styles.link}>Terms of Service</Text> and{' '}
              <Text style={styles.link}>Privacy Policy</Text>
            </Text>
          </View>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: spacing['3xl'],
  },
  logo: {
    fontSize: typography.fontSize['4xl'],
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[50],
    marginBottom: spacing.sm,
  },
  tagline: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[200],
    textAlign: 'center',
  },
  illustration: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  illustrationPlaceholder: {
    width: 200,
    height: 200,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 100,
  },
  footer: {
    paddingBottom: spacing.xl,
  },
  loginButton: {
    marginTop: spacing.md,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderColor: colors.gray[50],
  },
  terms: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[300],
    textAlign: 'center',
    marginTop: spacing.lg,
    lineHeight: typography.fontSize.xs * typography.lineHeight.relaxed,
  },
  link: {
    color: colors.gray[50],
    textDecorationLine: 'underline',
  },
});