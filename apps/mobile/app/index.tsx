import { View, Text, StyleSheet } from 'react-native';
import { Link } from 'expo-router';

export default function Index() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Bienvenido a Salonier</Text>
      <Text style={styles.subtitle}>Tu asistente de coloración con IA</Text>
      
      <Link href="/(auth)/welcome" style={styles.link}>
        <Text style={styles.linkText}>Ir a la pantalla de bienvenida</Text>
      </Link>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#0284c7',
  },
  subtitle: {
    fontSize: 18,
    color: '#6b7280',
    marginBottom: 40,
    textAlign: 'center',
  },
  link: {
    marginTop: 20,
  },
  linkText: {
    fontSize: 16,
    color: '#0284c7',
    textDecorationLine: 'underline',
  },
});