import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function Index() {
  // Versión simplificada para diagnosticar el problema
  return (
    <View style={styles.container}>
      <Text style={styles.title}>🎨 Salonier</Text>
      <Text style={styles.subtitle}>
        Tu asistente de coloración con IA
      </Text>

      <View style={styles.buttonsContainer}>
        <Link href="/(auth)/login" asChild>
          <TouchableOpacity style={styles.button}>
            <Text style={styles.buttonText}>Ir al <PERSON>gin</Text>
          </TouchableOpacity>
        </Link>

        <Link href="/(tabs)" asChild>
          <TouchableOpacity style={[styles.button, styles.secondaryButton]}>
            <Text style={[styles.buttonText, styles.secondaryButtonText]}>
              Ir al Dashboard (Demo)
            </Text>
          </TouchableOpacity>
        </Link>
      </View>

      <Text style={styles.debugText}>
        ✅ App funcionando - Navegación básica
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#ffffff',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
    color: '#0284c7',
  },
  subtitle: {
    fontSize: 18,
    marginBottom: 40,
    textAlign: 'center',
    color: '#6b7280',
  },
  buttonsContainer: {
    width: '100%',
    maxWidth: 300,
    gap: 16,
  },
  button: {
    backgroundColor: '#0284c7',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#0284c7',
  },
  secondaryButtonText: {
    color: '#0284c7',
  },
  debugText: {
    fontSize: 14,
    color: '#10b981',
    marginTop: 40,
    textAlign: 'center',
  },
});